// Copyright (c) Huawei Technologies Co., Ltd. 2023. All rights reserved.
// rubik licensed under the Mulan PSL v2.
// You can use this software according to the terms and conditions of the Mulan PSL v2.
// You may obtain a copy of Mulan PSL v2 at:
//     http://license.coscl.org.cn/MulanPSL2
// THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR
// IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT, MERCHANTABILITY OR FIT FOR A PARTICULAR
// PURPOSE.
// See the Mulan PSL v2 for more details.
// Author: Xi<PERSON> Li
// Create: 2023-02-21
// Description: This file will init cache limit directories before services running

// Package dyncache is the service used for cache limit setting
package dynamic

import (
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"k8s.io/klog"

	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/k8s-mpam-controller/util"
)

const (
	base2, base16, bitSize = 2, 16, 32
)

type limitSet struct {
	dir       string
	groupName string
	l3Percent int
	mbPercent int
}

func (c *DynCache) newCacheLimitSet(groupName string, l3Per, mbPer int) *limitSet {
	return &limitSet{
		groupName: groupName,
		l3Percent: l3Per,
		mbPercent: mbPer,
		dir:       filepath.Join(filepath.Clean(c.config.DefaultResctrlDir), resctrlDirPrefix+groupName),
	}
}

func (cl *limitSet) setDir() error {
	if err := os.Mkdir(cl.dir, util.DefaultDirMode); err != nil && !os.IsExist(err) {
		return fmt.Errorf("failed to create cache limit directory: %v", err)
	}
	return nil
}

func (cl *limitSet) writeResctrlSchemata(numaNum int) error {
	// get cbm mask like "fffff" means 20 cache way
	maskFile := filepath.Join(filepath.Dir(cl.dir), "info", "L3", "cbm_mask")
	llc, err := calcLimitedCacheValue(maskFile, cl.l3Percent)
	if err != nil {
		return fmt.Errorf("failed to get limited cache value from L3 percent: %v", err)
	}

	if err := cl.setDir(); err != nil {
		return err
	}
	schemetaFile := filepath.Join(cl.dir, schemataFileName)
	var content string
	var l3List, mbList []string
	for i := range numaNum {
		l3List = append(l3List, fmt.Sprintf("%d=%s", i, llc))
		mbList = append(mbList, fmt.Sprintf("%d=%d", i, cl.mbPercent))
	}
	l3 := fmt.Sprintf("L3:%s\n", strings.Join(l3List, ";"))
	mb := fmt.Sprintf("MB:%s\n", strings.Join(mbList, ";"))
	content = l3 + mb
	if err := util.WriteFile(schemetaFile, content); err != nil {
		return fmt.Errorf("failed to write %s to file %s: %v", content, schemetaFile, err)
	}

	return nil
}

func getNUMANum(path string) (int, error) {
	files, err := filepath.Glob(filepath.Join(path, "node*"))
	if err != nil {
		return 0, err
	}
	return len(files), nil
}

// getBinaryMask get l3 limit mask like "7ff" and transfer it to binary like "111 1111 1111", return binary length 11
func getBinaryMask(path string) (int, error) {
	maskValue, err := util.ReadFile(path)
	if err != nil {
		return -1, fmt.Errorf("failed to get L3 mask value: %v", err)
	}

	// transfer mask to binary format
	decMask, err := strconv.ParseInt(strings.TrimSpace(string(maskValue)), base16, bitSize)
	if err != nil {
		return -1, fmt.Errorf("failed to transfer L3 mask value %v to decimal format: %v", string(maskValue), err)
	}
	return len(strconv.FormatInt(decMask, base2)), nil
}

// calcLimitedCacheValue calculate number of cache way could be used according to L3 limit percent
func calcLimitedCacheValue(path string, l3Percent int) (string, error) {
	l3BinaryMask, err := getBinaryMask(path)
	if err != nil {
		return "", err
	}

	hundred := 100
	binLen := l3BinaryMask * l3Percent / hundred
	if binLen == 0 {
		binLen = 1
	}
	binValue := strings.Repeat("1", binLen)

	decValue, err := strconv.ParseInt(binValue, base2, bitSize)
	if err != nil {
		return "", fmt.Errorf("failed to transfer %v to decimal format: %v", binValue, err)
	}

	return strconv.FormatInt(decValue, base16), nil
}

func checkResctrlPath(path string) error {
	if !util.PathExist(path) {
		return fmt.Errorf("resctrl path %v does not exist, does not support cache limit", path)
	}
	schemataPath := filepath.Join(path, schemataFileName)
	if !util.PathExist(schemataPath) {
		return fmt.Errorf("resctrl schemata file %v does not exist, please check if %v directory is mounted",
			schemataPath, path)
	}
	return nil
}

func (c *DynCache) initDynCache() error {
	const (
		defaultL3PercentMax = 100
		defaultMbPercentMax = 100
	)

	if err := checkResctrlPath(c.config.DefaultResctrlDir); err != nil {
		return err
	}

	numaNum, err := getNUMANum(c.Attr.NumaNodeDir)
	if err != nil {
		return fmt.Errorf("failed to get NUMA nodes number: %s", err)
	}

	c.Attr.NumaNum = numaNum
	c.Attr.L3PercentDynamic = c.config.L3Percent.Low
	c.Attr.MemBandPercentDynamic = c.config.MemBandPercent.Low

	if err := c.setMPAMConfig(); err != nil {
		return fmt.Errorf("failed to set mpam config, err: %s", err)
	}

	dynamicMPAM := c.newCacheLimitSet(levelDynamic, c.Attr.L3PercentDynamic, c.Attr.MemBandPercentDynamic)
	if err := dynamicMPAM.writeResctrlSchemata(c.Attr.NumaNum); err != nil {
		return err
	}

	klog.Infof("init DynCache directory successfully")
	return nil
}

func (c *DynCache) setMPAMConfig() error {
	if !existConfigFile() {
		klog.Infof("no mpam config, use default config")
		return nil
	}
	configData, err := getConfigData()
	if err != nil {
		return err
	}
	mpamConfig, err := getMPAMConfig(configData)
	if err != nil {
		return err
	}

	if err := mpamConfig.validate(); err != nil {
		return err
	}

	c.Attr.MaxMiss = mpamConfig.CacheMiss.MaxMiss
	c.Attr.MinMiss = mpamConfig.CacheMiss.MinMiss

	c.config.AdjustInterval = mpamConfig.AdjustInterval
	c.config.PerfDuration = mpamConfig.PerfDuration

	c.config.L3Percent.High = mpamConfig.L3Percent.High
	c.config.L3Percent.Low = mpamConfig.L3Percent.Low
	c.config.MemBandPercent.High = mpamConfig.MemBandPercent.High
	c.config.MemBandPercent.Low = mpamConfig.MemBandPercent.Low

	return nil
}
