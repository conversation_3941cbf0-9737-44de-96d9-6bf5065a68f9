# Kunpeng-TAP 编译和部署指南

## 概述

Kunpeng-TAP 是一个为鲲鹏处理器优化的容器拓扑感知调度组件，支持 NUMA 感知和拓扑感知的容器资源分配策略。

## 项目结构

```
├── cmd/kunpeng-tap/
│   ├── manager/          # TAP 管理器
│   └── proxy/            # TAP 代理
├── pkg/kunpeng-tap/
│   ├── cache/            # 缓存管理
│   ├── policy/           # 调度策略
│   ├── server/           # 服务器实现
│   ├── monitoring/       # 监控指标
│   └── version/          # 版本信息
├── api/kunpeng-tap/      # API 定义
├── test/kunpeng-tap/     # 测试代码
└── hack/kunpeng-tap/     # 部署脚本
```

## 编译

### 前置条件

- Go 1.23.6 或更高版本
- Linux 环境（推荐 Ubuntu 18.04+）

### 使用 Makefile 编译

```bash
# 查看所有可用命令
make -f Makefile.kunpeng-tap help

# 编译所有组件
make -f Makefile.kunpeng-tap build

# 仅编译管理器
make -f Makefile.kunpeng-tap build-manager

# 仅编译代理
make -f Makefile.kunpeng-tap build-proxy

# 清理编译产物
make -f Makefile.kunpeng-tap clean
```

### 手动编译

```bash
# 编译管理器
go build -o bin/kunpeng-tap-manager ./cmd/kunpeng-tap/manager

# 编译代理
go build -o bin/kunpeng-tap-proxy ./cmd/kunpeng-tap/proxy
```

## 开发

### 代码格式化和检查

```bash
# 格式化代码
make -f Makefile.kunpeng-tap fmt

# 代码静态检查
make -f Makefile.kunpeng-tap vet

# 整理依赖
make -f Makefile.kunpeng-tap tidy
```

### 运行测试

```bash
# 运行单元测试
make -f Makefile.kunpeng-tap test

# 运行端到端测试
make -f Makefile.kunpeng-tap test-e2e
```

### 本地运行

```bash
# 运行管理器
make -f Makefile.kunpeng-tap run-manager

# 运行代理
make -f Makefile.kunpeng-tap run-proxy
```

## 部署

### 系统服务部署

#### Docker 运行时环境

```bash
# 安装服务（Docker 运行时）
sudo make -f Makefile.kunpeng-tap install-service-docker

# 启动服务
sudo make -f Makefile.kunpeng-tap start-service

# 查看服务状态
sudo make -f Makefile.kunpeng-tap status-service
```

#### Containerd 运行时环境

```bash
# 安装服务（Containerd 运行时）
sudo make -f Makefile.kunpeng-tap install-service-containerd

# 启动服务
sudo make -f Makefile.kunpeng-tap start-service
```

### 服务管理

```bash
# 启动服务
sudo make -f Makefile.kunpeng-tap start-service

# 停止服务
sudo make -f Makefile.kunpeng-tap stop-service

# 重启服务
sudo make -f Makefile.kunpeng-tap restart-service

# 查看服务状态
sudo make -f Makefile.kunpeng-tap status-service

# 卸载服务
sudo make -f Makefile.kunpeng-tap uninstall-service
```

## 容器化部署

### 构建 Docker 镜像

```bash
# 构建镜像
make -f Makefile.kunpeng-tap docker-build

# 推送镜像
make -f Makefile.kunpeng-tap docker-push

# 多架构构建
make -f Makefile.kunpeng-tap docker-buildx
```

## 配置

### 代理配置选项

- `--runtime-proxy-endpoint`: 运行时代理端点（默认：/var/run/kunpeng-tap/runtimeproxy.sock）
- `--container-runtime-service-endpoint`: 容器运行时服务端点
- `--container-runtime-mode`: 容器运行时模式（Docker|Containerd）
- `--resource-policy`: 资源策略（numa-aware|topology-aware）
- `--enable-memory-topology`: 启用内存拓扑感知

### 管理器配置选项

- `--policy-manager-endpoint`: 策略管理器端点
- `--container-runtime-service-endpoint`: 容器运行时服务端点
- `--resource-policy`: 资源策略

## 监控

Kunpeng-TAP 提供 Prometheus 指标，默认在 `:9091/metrics` 端点暴露。

### Topology-aware 策略监控指标

Topology-aware 策略提供了丰富的监控指标，帮助您监控容器在资源树中的分布情况和资源使用情况：

#### 容器分布指标
- `tap_topology_container_count`：每个 NUMA 节点上的容器数量
- 标签：`numa_node`, `socket_id`, `die_id`

#### 资源使用指标
- `tap_topology_cpu_usage_millicores`：每个 NUMA 节点的 CPU 使用情况
- `tap_topology_memory_usage_kb`：每个 NUMA 节点的内存使用情况
- 标签：`numa_node`, `socket_id`, `die_id`, `type` (CPU 指标)

#### 层次结构指标
- `tap_topology_hierarchy_resource_usage`：不同层次（socket、die、numa）的资源使用情况
- 标签：`hierarchy_level`, `node_id`, `resource_type`, `metric_type`

#### 性能指标
- `tap_topology_allocation_total`：资源分配成功/失败次数
- `tap_topology_propagation_duration_seconds`：资源传播操作延迟

### 监控查询示例

```promql
# 查看容器分布
sum by (numa_node) (tap_topology_container_count)

# 查看 CPU 使用率
sum by (numa_node) (tap_topology_cpu_usage_millicores{type="allocated"})

# 查看内存使用情况
sum by (numa_node) (tap_topology_memory_usage_kb)

# 计算分配成功率
sum(tap_topology_allocation_total{status="success"}) / sum(tap_topology_allocation_total) * 100
```

### 告警规则

系统提供了预定义的告警规则，包括：
- 高 CPU 使用率告警
- 高内存使用率告警
- 容器分布不平衡告警
- 资源分配失败告警

详细的监控配置和查询示例请参考 `config/kunpeng-tap/prometheus/topology_metrics_example.yaml`。

## 故障排除

### 常见问题

1. **编译失败**
   - 确保 Go 版本 >= 1.23.6
   - 运行 `make -f Makefile.kunpeng-tap tidy` 更新依赖

2. **服务启动失败**
   - 检查容器运行时是否正常运行
   - 确保有足够的权限访问容器运行时套接字

3. **权限问题**
   - 确保以 root 权限运行服务安装命令
   - 检查 systemd 服务文件权限

### 日志查看

```bash
# 查看服务日志
sudo journalctl -u kunpeng-tap.service -f

# 查看服务状态
sudo systemctl status kunpeng-tap.service
```

## 版本信息

```bash
# 查看版本信息
./bin/kunpeng-tap --version
./bin/kunpeng-tap-manager --version
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进 Kunpeng-TAP。 

## Docker Stop Container Hook

The `HandleStopContainer` method now supports hook functionality similar to `HandleCreateContainer`. When a Docker stop container request is received:

1. **Request Parsing**: The request is parsed to extract the container ID and timeout parameters using `utils.ParseDockerStopRequest()`
2. **Hook Request Preparation**: A hook request is prepared for the PostStopContainer policy via `dispatcher.ParseContainerRequest()`
3. **Docker Runtime Call**: The request is forwarded to the Docker runtime
4. **Response Analysis**: The Docker response is analyzed to determine the status:
   - 204: Success (container stopped)
   - 304: Container already stopped
   - 404: No such container
   - 500: Server error
5. **Policy Hook Execution**: If the stop operation was successful (204 or 304), the PostStopContainer hook is called with the container information from cache
6. **Cache Cleanup**: After successful stop operation, the container is removed from cache
7. **Response Forwarding**: The original Docker response is forwarded to the client

### Key Features

- **DockerStopRequest Structure**: Custom structure for parsing Docker stop container requests
- **Dispatcher Integration**: Full integration with the dispatcher for hook processing
- **Cache Management**: Automatic cache cleanup after successful stop operations
- **Error Handling**: Proper handling of different Docker response codes
- **Logging**: Comprehensive logging for debugging and monitoring

### Response Code Handling

The system correctly handles Docker Engine API response codes:
- **204**: Success - container stopped successfully
- **304**: Already stopped - container was already in stopped state
- **404**: Not found - container doesn't exist
- **500**: Server error - internal Docker error

Only successful operations (204 or 304) trigger the PostStopContainer hook and cache cleanup. 

## Resource Propagation Redesign

### Problem Analysis

The previous resource propagation logic was fundamentally flawed:

1. **Incorrect Reset Logic**: The `resetParentResourceUsage` method was missing the reset of `grantedShared` field, and had duplicate lines for `grantedMemory`.

2. **Resource Accumulation Bug**: The `updateParentResourceUsage` method was using `parentSupply.Collect(childSupply)` which accumulated all supply values, including already accumulated values from previous calls, leading to exponential growth of negative values.

3. **Repeated Collection**: Each time `propagateResourceUsageToParent` was called, it would re-collect all child resources without properly resetting the parent's accumulated values.

4. **Wrong Approach**: The logic was trying to recalculate parent resources by aggregating all child resources, which is inefficient and error-prone.

### New Design

The redesigned resource propagation logic follows a more efficient and correct approach:

1. **Incremental Updates**: Instead of recalculating parent resources from scratch, we now use incremental updates based on the grant's allocation information.

2. **Separate Allocation and Release**: 
   - **Allocation**: Use `propagateResourceUsageToParent` to increase parent resources based on grant allocation
   - **Release**: Use `propagateResourceReleaseToParent` to decrease parent resources based on grant allocation

3. **Direct Field Updates**: Directly update the parent's resource fields based on the grant's allocated resources, avoiding the complex aggregation logic.

### Implementation

#### Resource Allocation Propagation

```go
// propagateResourceUsageToParent - Increases parent resources based on grant allocation
func (p *TopologyAwarePolicy) propagateResourceUsageToParent(grant Grant) {
    // Traverse up the hierarchy and increment parent resources
    parent := currentNode.Parent()
    for parent != nil && !parent.IsNil() {
        p.updateParentResourceUsageByGrant(parent, grant)
        parent = parent.Parent()
    }
}

// updateParentResourceUsageByGrant - Increments parent resources
func (p *TopologyAwarePolicy) updateParentResourceUsageByGrant(parent Node, grant Grant) {
    if supply, ok := parentSupply.(*supply); ok {
        supply.grantedShared += grant.AllocatedCPUs()
        supply.grantedCPUByRequest += grant.AllocatedCPUByRequest()
        supply.grantedCPUByLimit += grant.AllocatedCPUByLimit()
        supply.grantedMemory += grant.AllocatedMemory()
    }
}
```

#### Resource Release Propagation

```go
// propagateResourceReleaseToParent - Decreases parent resources based on grant allocation
func (p *TopologyAwarePolicy) propagateResourceReleaseToParent(grant Grant) {
    // Traverse up the hierarchy and decrement parent resources
    parent := currentNode.Parent()
    for parent != nil && !parent.IsNil() {
        p.updateParentResourceUsageByRelease(parent, grant)
        parent = parent.Parent()
    }
}

// updateParentResourceUsageByRelease - Decrements parent resources
func (p *TopologyAwarePolicy) updateParentResourceUsageByRelease(parent Node, grant Grant) {
    if supply, ok := parentSupply.(*supply); ok {
        supply.grantedShared -= grant.AllocatedCPUs()
        supply.grantedCPUByRequest -= grant.AllocatedCPUByRequest()
        supply.grantedCPUByLimit -= grant.AllocatedCPUByLimit()
        supply.grantedMemory -= grant.AllocatedMemory()
        
        // Ensure values don't go below 0
        if supply.grantedShared < 0 { supply.grantedShared = 0 }
        if supply.grantedCPUByRequest < 0 { supply.grantedCPUByRequest = 0 }
        if supply.grantedCPUByLimit < 0 { supply.grantedCPUByLimit = 0 }
        if supply.grantedMemory < 0 { supply.grantedMemory = 0 }
    }
}
```

### Key Improvements

1. **Efficiency**: No more recalculating all child resources - just increment/decrement based on the specific grant
2. **Accuracy**: Direct field updates ensure precise resource accounting
3. **Simplicity**: Clear separation between allocation and release operations
4. **Safety**: Bounds checking prevents negative values
5. **Performance**: O(depth) operations instead of O(children * depth)

### Usage

- **During Container Creation**: `propagateResourceUsageToParent(grant)` is called after successful allocation
- **During Container Release**: `propagateResourceReleaseToParent(grant)` is called before releasing the grant

### Benefits

- **Correct Resource Accounting**: Parent nodes accurately reflect the sum of their children's allocations
- **No More Negative Values**: Proper bounds checking prevents resource values from going below 0
- **Better Performance**: Incremental updates are much faster than full recalculation
- **Maintainable Code**: Clear, simple logic that's easy to understand and debug
- **Stable Long-term Operation**: No accumulation errors during extended stress testing 