apiVersion: v1
kind: Namespace
metadata:
  name: rc-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: rc-config.default
  namespace: rc-config 
data:
  rc.conf: |
    mpam:
      group1:
        llc: "L3:0=1ff;1=1ff;2=1ff;3=1ff"
        mb: "MB:0=10;1=10;2=10;3=10"
      group2:
        llc: "L3:0=3ff;1=3ff;2=3ff;3=3ff"
        mb: "MB:0=20;1=20;2=20;3=20"
      group3:
        llc: "L3:0=7ff;1=7ff;2=7ff;3=7ff"
        mb: "MB:0=30;1=30;2=30;3=30"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: rc-config.group.clx
  namespace: rc-config
data:
  rc.conf: |
    mpam:
      group1:
        llc: "L3:0=1ff;1=1ff;2=1ff;3=1ff"
        mb: "MB:0=40;1=40;2=40;3=40"
      group2:
        llc: "L3:0=3ff;1=3ff;2=3ff;3=3ff"
        mb: "MB:0=50;1=50;2=50;3=50"
      group3:
        llc: "L3:0=7ff;1=7ff;2=7ff;3=7ff"
        mb: "MB:0=60;1=60;2=60;3=60"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: rc-config.group.icx
  namespace: rc-config
data:
  rc.conf: |
    mpam:
      group1:
        llc: "L3:0=1f;1=1f;2=1f;3=1f"
      group2:
        llc: "L3:0=3f;1=3f;2=3f;3=3f"
      group3:
        llc: "L3:0=ff;1=ff;2=ff;3=ff"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: rc-config.node.master
  namespace: rc-config
data:
  rc.conf: |
    mpam:
      group1:
        llc: "L3:0=1ff;1=1ff;2=1ff;3=1ff"
        mb: "MB:0=70;1=70;2=70;3=70"
      group2:
        llc: "L3:0=3ff;1=3ff;2=3ff;3=3ff"
        mb: "MB:0=80;1=80;2=80;3=80"
      group3:
        llc: "L3:0=7ff;1=7ff;2=7ff;3=7ff"
        mb: "MB:0=90;1=90;2=90;3=90"
