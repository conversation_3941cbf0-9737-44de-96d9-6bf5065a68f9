# Build k8s-mpam-controller
FROM golang:1.23.6 as builder

ENV PATH="/usr/local/go/bin:$PATH"
ENV CGO_ENABLE=1
ENV GOBIN="/go/bin"

WORKDIR /go/src/k8s-mpam-controller
COPY . /go/src/k8s-mpam-controller

# use "go get" instead of "go mod" to bypass client-go dependence issue
# use build.sh to bypass error: "The command '/bin/sh -c go get -d -v ./...' returned a non-zero code: 1"
RUN ./hack/build-mpam.sh

# Create k8s-mpam-controller image
FROM openeuler-22.03-lts-sp3:latest

COPY --from=builder /go/bin/k8s-mpam-controller /usr/bin/agent
