# Topology-aware 策略资源树监控系统实现

## 🔍 **设计目标**

### 核心需求
1. **以资源树为中心的监控**：直接以资源树节点为监控对象
2. **降低函数复杂度**：将监控逻辑从业务逻辑中分离
3. **良好的代码风格**：清晰的命名、完整的注释、完善的错误处理
4. **避免重复和冲突**：删除旧的监控指标，使用新的资源树为中心的指标

### 实现原则
1. **职责分离**：创建专门的 `TopologyMetricsManager` 类
2. **模块化设计**：每个函数只负责一个特定的监控任务
3. **线程安全**：使用互斥锁保护共享资源
4. **向后兼容**：保持与现有系统的兼容性

## 🔧 **解决方案**

### 1. **创建资源树监控管理器**
- 创建专门的 `TopologyMetricsManager` 类
- 将监控逻辑从业务逻辑中完全分离
- 提供线程安全的指标更新方法

```go
type TopologyMetricsManager struct {
    policy *TopologyAwarePolicy
    mu     sync.RWMutex
}

func NewTopologyMetricsManager(policy *TopologyAwarePolicy) *TopologyMetricsManager {
    return &TopologyMetricsManager{
        policy: policy,
    }
}
```

### 2. **删除旧的监控指标**
- 移除 `monitoring/metrics.go` 中的旧指标定义
- 删除 `TopologyContainerCount`、`TopologyCPUUsage`、`TopologyMemoryUsage` 等旧指标
- 避免与新的资源树指标产生重复和冲突

### 3. **实现新的资源树监控指标**
- `tap_topology_node_status` - 资源树节点状态
- `tap_topology_node_capacity` - 资源树节点容量
- `tap_topology_node_usage` - 资源树节点使用情况
- `tap_topology_node_container_count` - 资源树节点容器分布
- `tap_topology_hierarchy_info` - 资源树层次结构信息
- `tap_topology_allocation_v2_total` - 资源分配成功率（V2）

### 4. **基于事件触发的指标更新**
- 移除 goroutine 定期更新方式，避免 goroutine 泄漏风险
- 在关键操作时使用 `defer` 方式触发指标更新

```go
// 在 PreCreateContainerHook 中
func (p *TopologyAwarePolicy) PreCreateContainerHook(ctx policy.HookContext) (*policy.Allocation, error) {
    // 使用 defer 确保在函数结束时更新指标
    defer func() {
        p.updateAllMetrics()
    }()
    
    // ... 资源分配逻辑 ...
}
```

### 5. **简化的监控启动**
- `startMetricsMonitoring()` 方法只执行一次初始更新
- 后续更新通过事件触发

```go
func (p *TopologyAwarePolicy) startMetricsMonitoring() {
    klog.InfoS("Topology-aware policy monitoring enabled",
        "policy", p.Name(),
        "description", p.Description())

    // 立即执行一次指标更新
    if p.metricsManager != nil {
        p.metricsManager.UpdateAllMetrics()
    }
}
```

### 2. **基于事件触发的指标更新**
- 移除 goroutine 定期更新方式，避免 goroutine 泄漏风险
- 在关键操作时使用 `defer` 方式触发指标更新
- 类似于 `defer func(){}` 的模式

```go
// 在 PreCreateContainerHook 中
func (p *TopologyAwarePolicy) PreCreateContainerHook(ctx policy.HookContext) (*policy.Allocation, error) {
    // 使用 defer 确保在函数结束时更新指标
    defer func() {
        p.updateAllMetrics()
    }()
    
    // ... 资源分配逻辑 ...
}

// 在 PostStopContainerHook 中
func (p *TopologyAwarePolicy) PostStopContainerHook(ctx policy.HookContext) (*policy.Allocation, error) {
    // 使用 defer 确保在函数结束时更新指标
    defer func() {
        p.updateAllMetrics()
    }()
    
    // ... 资源释放逻辑 ...
}

// 在 ReleaseResources 中
func (p *TopologyAwarePolicy) ReleaseResources(containerCtx policy.ContainerContext) error {
    // 使用 defer 确保在函数结束时更新指标
    defer func() {
        p.updateAllMetrics()
    }()
    
    // ... 资源释放逻辑 ...
}
```

### 3. **简化的监控启动**
- `startMetricsMonitoring()` 方法只执行一次初始更新
- 后续更新通过事件触发

```go
func (p *TopologyAwarePolicy) startMetricsMonitoring() {
    klog.InfoS("Topology-aware policy monitoring enabled",
        "policy", p.Name(),
        "description", p.Description())

    // 立即执行一次指标更新
    p.updateAllMetrics()
}
```

### 4. **实现指标更新方法**
- `updateAllMetrics()` 方法遍历所有 grants 和 pools
- 更新容器分布、CPU 使用、内存使用等指标

```go
func (p *TopologyAwarePolicy) updateAllMetrics() {
    // 重置容器计数指标
    monitoring.TopologyContainerCount.Reset()

    // 遍历所有 grants 来统计容器分布
    p.allocations.grants.Range(func(_, grantVal interface{}) bool {
        grant := grantVal.(Grant)
        node := grant.GetNode()
        if node == nil {
            return true
        }

        numaNode, socketID, dieID := p.getNodeHierarchyInfo(node)
        monitoring.TopologyContainerCount.WithLabelValues(numaNode, socketID, dieID).Inc()
        return true
    })

    // 更新资源使用指标
    for _, node := range p.pools {
        supply := node.FreeResource()
        if supply == nil {
            continue
        }

        numaNode, socketID, dieID := p.getNodeHierarchyInfo(node)
        
        // 更新 CPU 使用指标
        monitoring.TopologyCPUUsage.WithLabelValues(numaNode, socketID, dieID, "allocated").Set(float64(supply.GrantedShared()))
        monitoring.TopologyCPUUsage.WithLabelValues(numaNode, socketID, dieID, "request").Set(float64(supply.GrantedCPUByRequest()))
        monitoring.TopologyCPUUsage.WithLabelValues(numaNode, socketID, dieID, "limit").Set(float64(supply.GrantedCPUByLimit()))
        
        // 更新内存使用指标
        monitoring.TopologyMemoryUsage.WithLabelValues(numaNode, socketID, dieID).Set(float64(supply.GrantedMemory()))
    }
}
```

### 5. **在资源分配时记录指标**
- 在 `recordAllocationMetrics()` 方法中直接调用监控指标

```go
func (p *TopologyAwarePolicy) recordAllocationMetrics(grant Grant, status string) {
    // ... 获取节点信息 ...
    
    // 直接调用监控指标
    monitoring.TopologyAllocationSuccess.WithLabelValues(status, numaNode, socketID).Inc()
    
    klog.V(4).InfoS("Recorded allocation metrics", ...)
}
```

### 6. **添加安全检查**
- 在 `getNodeHierarchyInfo()` 方法中添加 nil 检查
- 确保即使传入 nil 节点也不会 panic

```go
func (p *TopologyAwarePolicy) getNodeHierarchyInfo(node Node) (numaNode, socketID, dieID string) {
    numaNode = "unknown"
    socketID = "unknown"
    dieID = "unknown"

    // 检查节点是否为 nil
    if node == nil || node.IsNil() {
        return numaNode, socketID, dieID
    }
    
    // ... 处理节点信息 ...
}
```

## 📊 **监控指标列表**

所有指标都在 `monitoring/metrics.go` 中定义：

### 1. 容器分布指标
- **名称**: `tap_topology_container_count`
- **类型**: Gauge
- **标签**: `numa_node`, `socket_id`, `die_id`
- **描述**: 每个 NUMA 节点上的容器数量

### 2. CPU 资源使用指标
- **名称**: `tap_topology_cpu_usage_millicores`
- **类型**: Gauge
- **标签**: `numa_node`, `socket_id`, `die_id`, `type`
- **描述**: 每个 NUMA 节点的 CPU 使用情况（毫核）

### 3. 内存资源使用指标
- **名称**: `tap_topology_memory_usage_kb`
- **类型**: Gauge
- **标签**: `numa_node`, `socket_id`, `die_id`
- **描述**: 每个 NUMA 节点的内存使用情况（KB）

### 4. 资源分配成功率指标
- **名称**: `tap_topology_allocation_total`
- **类型**: Counter
- **标签**: `status`, `numa_node`, `socket_id`
- **描述**: 资源分配的成功/失败次数

### 5. 层次结构资源使用指标
- **名称**: `tap_topology_hierarchy_resource_usage`
- **类型**: Gauge
- **标签**: `hierarchy_level`, `node_id`, `resource_type`, `metric_type`
- **描述**: 不同层次上的资源使用情况

### 6. 资源传播延迟指标
- **名称**: `tap_topology_propagation_duration_seconds`
- **类型**: Histogram
- **标签**: `operation`, `hierarchy_level`
- **描述**: 资源传播操作的持续时间

## 🚀 **使用方法**

### 1. 部署后验证
```bash
# 检查 metrics 端点
curl http://localhost:8080/metrics | grep tap_topology

# 应该能看到类似输出：
# tap_topology_allocation_total{status="success",numa_node="0",socket_id="0"} 1
# tap_topology_container_count{numa_node="0",socket_id="0",die_id="0"} 2
# tap_topology_cpu_usage_millicores{numa_node="0",socket_id="0",die_id="0",type="allocated"} 1000
```

### 2. Prometheus 配置
```yaml
scrape_configs:
  - job_name: 'kunpeng-tap'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 15s
```

### 3. Grafana 查询示例
```promql
# 查看容器分布
sum by (numa_node) (tap_topology_container_count)

# 查看 CPU 使用率
sum by (numa_node) (tap_topology_cpu_usage_millicores{type="allocated"})

# 查看分配成功率
sum(tap_topology_allocation_total{status="success"}) / sum(tap_topology_allocation_total) * 100

# 查看层次结构资源使用
tap_topology_hierarchy_resource_usage

# 查看资源传播延迟
histogram_quantile(0.95, tap_topology_propagation_duration_seconds)
```

## ✅ **验证步骤**

1. **编译测试**：确保代码能正常编译
2. **单元测试**：运行测试确保监控功能正常
3. **部署验证**：部署后检查 `/metrics` 端点
4. **Prometheus 验证**：确认 Prometheus 能采集到指标
5. **Grafana 验证**：在 Grafana 中查询指标

## 🎯 **关键改进**

1. **避免重复定义**：直接使用 `monitoring` 包中的指标
2. **事件驱动更新**：在关键操作时触发指标更新，避免 goroutine 泄漏
3. **安全实现**：添加 nil 检查和错误处理
4. **完整覆盖**：包含容器分布、资源使用、分配成功率等指标
5. **易于维护**：清晰的代码结构和文档
6. **统一管理**：所有指标在 `monitoring/metrics.go` 中统一管理
7. **高效更新**：只在需要时更新指标，避免不必要的计算开销

## 🔄 **更新触发点**

### 容器创建时
- `PreCreateContainerHook()` - 容器创建前
- `AllocateResources()` - 资源分配时

### 容器停止时
- `PostStopContainerHook()` - 容器停止后
- `ReleaseResources()` - 资源释放时

### 初始化时
- `startMetricsMonitoring()` - 策略初始化时

通过这些改进，topology-aware 策略现在能够正确地向 Prometheus 暴露监控指标，用户可以在 Grafana 中看到容器在资源树中的分布情况和资源使用情况。同时避免了重复定义和 goroutine 泄漏风险，使代码更加简洁、安全和高效。 