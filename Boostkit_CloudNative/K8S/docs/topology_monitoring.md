# Topology-aware 策略资源树监控系统

## 概述

为了提高 topology-aware 策略的可观测性，我们实现了一个以资源树为中心的监控系统。该系统直接以资源树为指标来源，提供更准确、更完整的监控数据。

## 监控指标

### 1. 资源树节点状态指标

**指标名称**: `tap_topology_node_status`

**描述**: 显示资源树中每个节点的活跃状态

**标签**:
- `node_name`: 节点名称
- `node_type`: 节点类型（virtual/socket/die/numa）
- `node_id`: 节点ID
- `hierarchy_level`: 层次级别
- `parent_node`: 父节点名称

**查询示例**:
```promql
# 查看所有活跃的节点
tap_topology_node_status

# 查看特定类型的节点
tap_topology_node_status{node_type="numa"}

# 查看特定层次的节点
tap_topology_node_status{hierarchy_level="socket"}
```

### 2. 资源树节点容量指标

**指标名称**: `tap_topology_node_capacity`

**描述**: 显示每个节点的资源容量

**标签**:
- `node_name`: 节点名称
- `node_type`: 节点类型
- `node_id`: 节点ID
- `hierarchy_level`: 层次级别
- `resource_type`: 资源类型（cpu/memory）
- `capacity_type`: 容量类型（total_cores/total_kb）

**查询示例**:
```promql
# 查看所有节点的 CPU 容量
tap_topology_node_capacity{resource_type="cpu"}

# 查看所有节点的内存容量
tap_topology_node_capacity{resource_type="memory"}

# 查看特定节点的资源容量
tap_topology_node_capacity{node_name="NUMA node #0"}
```

### 3. 资源树节点使用指标

**指标名称**: `tap_topology_node_usage`

**描述**: 显示每个节点的资源使用情况

**标签**:
- `node_name`: 节点名称
- `node_type`: 节点类型
- `node_id`: 节点ID
- `hierarchy_level`: 层次级别
- `resource_type`: 资源类型（cpu/memory）
- `usage_type`: 使用类型（allocated_millicores/request_millicores/limit_millicores/allocated_kb）

**查询示例**:
```promql
# 查看 CPU 使用情况
tap_topology_node_usage{resource_type="cpu"}

# 查看内存使用情况
tap_topology_node_usage{resource_type="memory"}

# 查看已分配的 CPU 资源
tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"}
```

### 4. 资源树节点容器分布指标

**指标名称**: `tap_topology_node_container_count`

**描述**: 显示每个节点上的容器数量

**标签**:
- `node_name`: 节点名称
- `node_type`: 节点类型
- `node_id`: 节点ID
- `hierarchy_level`: 层次级别
- `container_state`: 容器状态（running）

**查询示例**:
```promql
# 查看所有节点的容器分布
tap_topology_node_container_count

# 查看特定类型节点的容器分布
tap_topology_node_container_count{node_type="numa"}

# 查看容器分布最多的节点
topk(5, tap_topology_node_container_count)
```

### 5. 资源树层次结构指标

**指标名称**: `tap_topology_hierarchy_info`

**描述**: 显示资源树层次结构的信息

**标签**:
- `hierarchy_level`: 层次级别
- `node_count`: 该层次的节点数量
- `total_depth`: 总深度
- `max_children`: 最大子节点数

**查询示例**:
```promql
# 查看层次结构信息
tap_topology_hierarchy_info

# 查看特定层次的信息
tap_topology_hierarchy_info{hierarchy_level="socket"}
```

### 6. 资源分配成功率指标（V2）

**指标名称**: `tap_topology_allocation_v2_total`

**描述**: 显示资源分配的成功/失败次数（基于节点信息）

**标签**:
- `status`: 分配状态（success/failed）
- `node_name`: 节点名称
- `node_type`: 节点类型
- `hierarchy_level`: 层次级别

**查询示例**:
```promql
# 查看分配成功率
sum(tap_topology_allocation_v2_total{status="success"}) / 
sum(tap_topology_allocation_v2_total) * 100

# 查看特定节点的分配情况
tap_topology_allocation_v2_total{node_name="NUMA node #0"}
```
- `socket_id`: Socket ID

**查询示例**:
```promql
# 查看分配成功率
sum(tap_topology_allocation_total{status="success"}) / 
sum(tap_topology_allocation_total) * 100

# 查看分配失败率
rate(tap_topology_allocation_total{status="failed"}[5m])
```

### 6. 资源传播延迟指标

**指标名称**: `tap_topology_propagation_duration_seconds`

**描述**: 显示资源传播操作的延迟时间

**标签**:
- `operation`: 操作类型（allocate/release）
- `hierarchy_level`: 层次级别（numa/socket/die）

**查询示例**:
```promql
# 查看传播延迟的 95 分位数
histogram_quantile(0.95, sum by (operation, hierarchy_level) 
(rate(tap_topology_propagation_duration_seconds_bucket[5m])))
```

## 告警规则

### 1. 高 CPU 使用率告警

```yaml
- alert: HighCPUUsage
  expr: sum by (numa_node) (tap_topology_cpu_usage_millicores{type="allocated"}) / 
        sum by (numa_node) (tap_topology_cpu_usage_millicores{type="limit"}) > 0.8
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "High CPU usage on NUMA node {{ $labels.numa_node }}"
    description: "CPU usage is above 80% on NUMA node {{ $labels.numa_node }}"
```

### 2. 高内存使用率告警

```yaml
- alert: HighMemoryUsage
  expr: sum by (numa_node) (tap_topology_memory_usage_kb) > 1000000
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "High memory usage on NUMA node {{ $labels.numa_node }}"
    description: "Memory usage is above 1GB on NUMA node {{ $labels.numa_node }}"
```

### 3. 容器分布不平衡告警

```yaml
- alert: ContainerDistributionImbalance
  expr: max(tap_topology_container_count) - min(tap_topology_container_count) > 5
  for: 10m
  labels:
    severity: warning
  annotations:
    summary: "Container distribution imbalance detected"
    description: "Container distribution is imbalanced across NUMA nodes"
```

### 4. 资源分配失败告警

```yaml
- alert: AllocationFailure
  expr: rate(tap_topology_allocation_total{status="failed"}[5m]) > 0
  for: 1m
  labels:
    severity: critical
  annotations:
    summary: "Resource allocation failures detected"
    description: "Resource allocation failures are occurring"
```

## Grafana 仪表板

### 推荐的仪表板面板

1. **容器分布饼图**
   - 查询: `sum by (numa_node) (tap_topology_container_count)`
   - 类型: Pie chart

2. **CPU 使用率趋势图**
   - 查询: `sum by (numa_node) (tap_topology_cpu_usage_millicores{type="allocated"})`
   - 类型: Time series

3. **内存使用率趋势图**
   - 查询: `sum by (numa_node) (tap_topology_memory_usage_kb)`
   - 类型: Time series

4. **层次结构资源使用热力图**
   - 查询: `tap_topology_hierarchy_resource_usage{resource_type="cpu", metric_type="allocated"}`
   - 类型: Heatmap

5. **分配成功率仪表**
   - 查询: `sum(tap_topology_allocation_total{status="success"}) / sum(tap_topology_allocation_total) * 100`
   - 类型: Gauge

## 使用方法

### 1. 启用监控

监控功能在 topology-aware 策略初始化时自动启用。您可以通过以下方式访问指标：

```bash
# 查看所有指标
curl http://localhost:8080/metrics

# 过滤 topology 相关指标
curl http://localhost:8080/metrics | grep tap_topology
```

### 2. 配置 Prometheus

在 Prometheus 配置文件中添加 scrape 配置：

```yaml
scrape_configs:
  - job_name: 'kunpeng-tap'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 15s
```

### 3. 配置告警

将告警规则添加到 Prometheus 或 Alertmanager 配置中。

## 监控最佳实践

### 1. 定期检查指标

- 监控容器分布是否均衡
- 检查 CPU 和内存使用率
- 关注分配失败率

### 2. 设置合适的告警阈值

- CPU 使用率告警阈值：80%
- 内存使用率告警阈值：1GB
- 容器分布不平衡阈值：5 个容器

### 3. 性能优化

- 定期检查传播延迟
- 监控分配成功率
- 关注资源使用趋势

## 故障排除

### 1. 指标不显示

- 检查服务是否正常运行
- 确认 `/metrics` 端点可访问
- 验证 Prometheus 配置

### 2. 指标值异常

- 检查 topology-aware 策略是否正常初始化
- 验证系统拓扑信息是否正确
- 查看服务日志

### 3. 告警不触发

- 检查告警规则语法
- 确认指标名称和标签正确
- 验证 Prometheus 配置

## 扩展功能

### 1. 自定义指标

您可以通过扩展 `TopologyMetricsManager` 来添加自定义指标：

```go
// 添加自定义指标
var CustomMetric = promauto.NewGaugeVec(
    prometheus.GaugeOpts{
        Name: "tap_custom_metric",
        Help: "Custom topology metric",
    },
    []string{"label1", "label2"},
)
```

### 2. 指标聚合

可以添加聚合指标来提供更高层次的视图：

```go
// 添加聚合指标
var AggregatedMetric = promauto.NewGaugeVec(
    prometheus.GaugeOpts{
        Name: "tap_aggregated_metric",
        Help: "Aggregated topology metric",
    },
    []string{"aggregation_type"},
)
```

## 总结

通过这套监控系统，您可以：

1. **实时监控**：了解容器在资源树中的分布情况
2. **性能分析**：分析 CPU 和内存使用模式
3. **问题诊断**：快速识别资源分配问题
4. **容量规划**：基于历史数据规划资源容量
5. **告警通知**：及时发现问题并采取措施

这套监控系统为 topology-aware 策略提供了全面的可观测性，帮助您更好地管理和优化容器资源分配。 