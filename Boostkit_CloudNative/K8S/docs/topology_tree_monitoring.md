# Topology-aware 策略资源树监控系统

## 概述

为了提高 topology-aware 策略的可观测性，我们实现了一个以资源树为中心的监控系统。该系统直接以资源树为指标来源，提供更准确、更完整的监控数据。

## 设计理念

### 1. 以资源树为中心
- **指标来源**：直接以资源树节点为监控对象
- **层次结构**：完整反映 socket、die、numa 的层次关系
- **状态监控**：监控每个节点的活跃状态和资源使用情况

### 2. 降低函数复杂度
- **职责分离**：将监控逻辑从业务逻辑中分离
- **模块化设计**：创建专门的 `TopologyMetricsManager`
- **单一职责**：每个函数只负责一个特定的监控任务

### 3. 良好的代码风格
- **清晰的命名**：函数和变量名称具有描述性
- **完整的注释**：每个函数都有详细的注释说明
- **错误处理**：包含完善的错误处理和边界检查

## 监控指标

### 1. 资源树节点状态指标

**指标名称**: `tap_topology_node_status`

**描述**: 显示资源树中每个节点的活跃状态

**标签**:
- `node_name`: 节点名称
- `node_type`: 节点类型（virtual/socket/die/numa）
- `node_id`: 节点ID
- `hierarchy_level`: 层次级别
- `parent_node`: 父节点名称

**查询示例**:
```promql
# 查看所有活跃的节点
tap_topology_node_status

# 查看特定类型的节点
tap_topology_node_status{node_type="numa"}

# 查看特定层次的节点
tap_topology_node_status{hierarchy_level="socket"}
```

### 2. 资源树节点容量指标

**指标名称**: `tap_topology_node_capacity`

**描述**: 显示每个节点的资源容量

**标签**:
- `node_name`: 节点名称
- `node_type`: 节点类型
- `node_id`: 节点ID
- `hierarchy_level`: 层次级别
- `resource_type`: 资源类型（cpu/memory）
- `capacity_type`: 容量类型（total_cores/total_kb）

**查询示例**:
```promql
# 查看所有节点的 CPU 容量
tap_topology_node_capacity{resource_type="cpu"}

# 查看所有节点的内存容量
tap_topology_node_capacity{resource_type="memory"}

# 查看特定节点的资源容量
tap_topology_node_capacity{node_name="NUMA node #0"}
```

### 3. 资源树节点使用指标

**指标名称**: `tap_topology_node_usage`

**描述**: 显示每个节点的资源使用情况

**标签**:
- `node_name`: 节点名称
- `node_type`: 节点类型
- `node_id`: 节点ID
- `hierarchy_level`: 层次级别
- `resource_type`: 资源类型（cpu/memory）
- `usage_type`: 使用类型（allocated_millicores/request_millicores/limit_millicores/allocated_kb）

**查询示例**:
```promql
# 查看 CPU 使用情况
tap_topology_node_usage{resource_type="cpu"}

# 查看内存使用情况
tap_topology_node_node_usage{resource_type="memory"}

# 查看已分配的 CPU 资源
tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"}
```

### 4. 资源树节点容器分布指标

**指标名称**: `tap_topology_node_container_count`

**描述**: 显示每个节点上的容器数量

**标签**:
- `node_name`: 节点名称
- `node_type`: 节点类型
- `node_id`: 节点ID
- `hierarchy_level`: 层次级别
- `container_state`: 容器状态（running）

**查询示例**:
```promql
# 查看所有节点的容器分布
tap_topology_node_container_count

# 查看特定类型节点的容器分布
tap_topology_node_container_count{node_type="numa"}

# 查看容器分布最多的节点
topk(5, tap_topology_node_container_count)
```

### 5. 资源树层次结构指标

**指标名称**: `tap_topology_hierarchy_info`

**描述**: 显示资源树层次结构的信息

**标签**:
- `hierarchy_level`: 层次级别
- `node_count`: 该层次的节点数量
- `total_depth`: 总深度
- `max_children`: 最大子节点数

**查询示例**:
```promql
# 查看层次结构信息
tap_topology_hierarchy_info

# 查看特定层次的信息
tap_topology_hierarchy_info{hierarchy_level="socket"}
```

### 6. 资源分配成功率指标（V2）

**指标名称**: `tap_topology_allocation_v2_total`

**描述**: 显示资源分配的成功/失败次数（基于节点信息）

**标签**:
- `status`: 分配状态（success/failed）
- `node_name`: 节点名称
- `node_type`: 节点类型
- `hierarchy_level`: 层次级别

**查询示例**:
```promql
# 查看分配成功率
sum(tap_topology_allocation_v2_total{status="success"}) / 
sum(tap_topology_allocation_v2_total) * 100

# 查看特定节点的分配情况
tap_topology_allocation_v2_total{node_name="NUMA node #0"}
```

## 架构设计

### 1. TopologyMetricsManager

```go
type TopologyMetricsManager struct {
    policy *TopologyAwarePolicy
    mu     sync.RWMutex
}
```

**职责**:
- 管理所有以资源树为中心的监控指标
- 提供线程安全的指标更新方法
- 分离监控逻辑与业务逻辑

### 2. 核心方法

#### UpdateAllMetrics()
- 更新所有监控指标
- 重置指标后重新收集数据
- 按顺序更新各类指标

#### updateNodeStatusMetrics()
- 更新节点状态指标
- 遍历资源树的所有节点
- 设置每个节点的活跃状态

#### updateNodeCapacityMetrics()
- 更新节点容量指标
- 收集 CPU 和内存容量信息
- 按节点类型分类统计

#### updateNodeUsageMetrics()
- 更新节点使用指标
- 收集已分配的 CPU 和内存
- 区分不同类型的资源使用

#### updateContainerDistributionMetrics()
- 更新容器分布指标
- 统计每个节点上的容器数量
- 基于 grants 信息计算分布

#### updateHierarchyInfoMetrics()
- 更新层次结构信息
- 统计各层次的节点数量
- 计算树的深度和最大子节点数

### 3. 集成方式

```go
// 在 TopologyAwarePolicy 中集成
type TopologyAwarePolicy struct {
    // ... 其他字段
    metricsManager *TopologyMetricsManager
}

// 初始化时创建监控管理器
func NewTopologyAwarePolicy(c cache.Cache, opts *policy.PolicyOptions) policy.Policy {
    // ... 其他初始化代码
    
    // 初始化资源树监控管理器
    p.metricsManager = NewTopologyMetricsManager(p)
    
    return p
}
```

## 使用方法

### 1. 启用监控

监控功能在策略初始化时自动启用：

```go
// 策略初始化时会自动创建监控管理器
policy := NewTopologyAwarePolicy(cache, options)
```

### 2. 指标更新

指标在以下时机自动更新：

```go
// 在资源分配时更新指标
func (p *TopologyAwarePolicy) PreCreateContainerHook(ctx policy.HookContext) (*policy.Allocation, error) {
    defer func() {
        p.updateAllMetrics() // 自动更新指标
    }()
    
    // ... 资源分配逻辑
}

// 在资源释放时更新指标
func (p *TopologyAwarePolicy) PostStopContainerHook(ctx policy.HookContext) (*policy.Allocation, error) {
    defer func() {
        p.updateAllMetrics() // 自动更新指标
    }()
    
    // ... 资源释放逻辑
}
```

### 3. 访问指标

```bash
# 查看所有指标
curl http://localhost:8080/metrics

# 过滤资源树相关指标
curl http://localhost:8080/metrics | grep tap_topology_node
```

## 监控查询示例

### 1. 资源使用率分析

```promql
# CPU 使用率
sum by (node_name) (tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"}) / 
sum by (node_name) (tap_topology_node_capacity{resource_type="cpu", capacity_type="total_cores"}) * 1000

# 内存使用率
sum by (node_name) (tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"}) / 
sum by (node_name) (tap_topology_node_capacity{resource_type="memory", capacity_type="total_kb"}) * 100
```

### 2. 容器分布分析

```promql
# 容器分布热力图
tap_topology_node_container_count

# 容器分布最多的前5个节点
topk(5, tap_topology_node_container_count)

# 按层次统计容器分布
sum by (hierarchy_level) (tap_topology_node_container_count)
```

### 3. 资源树结构分析

```promql
# 查看资源树结构
tap_topology_hierarchy_info

# 查看各层次的节点数量
sum by (hierarchy_level) (tap_topology_node_status)
```

### 4. 分配成功率分析

```promql
# 总体分配成功率
sum(tap_topology_allocation_v2_total{status="success"}) / 
sum(tap_topology_allocation_v2_total) * 100

# 按节点类型的分配成功率
sum by (node_type) (tap_topology_allocation_v2_total{status="success"}) / 
sum by (node_type) (tap_topology_allocation_v2_total) * 100
```

## 告警规则

### 1. 高资源使用率告警

```yaml
- alert: HighCPUUsage
  expr: sum by (node_name) (tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"}) / 
        sum by (node_name) (tap_topology_node_capacity{resource_type="cpu", capacity_type="total_cores"}) * 1000 > 80
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "High CPU usage on {{ $labels.node_name }}"
    description: "CPU usage is above 80% on {{ $labels.node_name }}"

- alert: HighMemoryUsage
  expr: sum by (node_name) (tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"}) / 
        sum by (node_name) (tap_topology_node_capacity{resource_type="memory", capacity_type="total_kb"}) * 100 > 80
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "High memory usage on {{ $labels.node_name }}"
    description: "Memory usage is above 80% on {{ $labels.node_name }}"
```

### 2. 容器分布不平衡告警

```yaml
- alert: ContainerDistributionImbalance
  expr: max(tap_topology_node_container_count) - min(tap_topology_node_container_count) > 5
  for: 10m
  labels:
    severity: warning
  annotations:
    summary: "Container distribution imbalance detected"
    description: "Container distribution is imbalanced across nodes"
```

### 3. 资源分配失败告警

```yaml
- alert: AllocationFailure
  expr: rate(tap_topology_allocation_v2_total{status="failed"}[5m]) > 0
  for: 1m
  labels:
    severity: critical
  annotations:
    summary: "Resource allocation failures detected"
    description: "Resource allocation failures are occurring"
```

## Grafana 仪表板

### 1. 资源树概览面板

**查询**:
```promql
# 节点状态
tap_topology_node_status

# 容器分布
tap_topology_node_container_count

# 层次结构信息
tap_topology_hierarchy_info
```

**面板类型**: 表格/热力图

### 2. 资源使用率面板

**查询**:
```promql
# CPU 使用率
sum by (node_name) (tap_topology_node_usage{resource_type="cpu", usage_type="allocated_millicores"}) / 
sum by (node_name) (tap_topology_node_capacity{resource_type="cpu", capacity_type="total_cores"}) * 1000

# 内存使用率
sum by (node_name) (tap_topology_node_usage{resource_type="memory", usage_type="allocated_kb"}) / 
sum by (node_name) (tap_topology_node_capacity{resource_type="memory", capacity_type="total_kb"}) * 100
```

**面板类型**: 时间序列图

### 3. 分配成功率面板

**查询**:
```promql
# 分配成功率
sum(tap_topology_allocation_v2_total{status="success"}) / 
sum(tap_topology_allocation_v2_total) * 100
```

**面板类型**: 仪表盘

## 优势总结

### 1. 以资源树为中心
- ✅ 直接以资源树节点为监控对象
- ✅ 完整反映层次结构关系
- ✅ 提供更准确的资源使用数据

### 2. 降低函数复杂度
- ✅ 职责分离，监控逻辑独立
- ✅ 模块化设计，易于维护
- ✅ 单一职责原则

### 3. 良好的代码风格
- ✅ 清晰的命名和注释
- ✅ 完善的错误处理
- ✅ 线程安全的实现

### 4. 向后兼容
- ✅ 保持原有指标不变
- ✅ 新增指标不影响现有功能
- ✅ 平滑升级体验

## 总结

新的资源树监控系统为 topology-aware 策略提供了更准确、更完整的监控能力。通过以资源树为中心的设计，我们能够：

1. **准确监控**：直接以资源树节点为监控对象，提供更准确的数据
2. **完整覆盖**：监控节点状态、资源容量、使用情况、容器分布等各个方面
3. **降低复杂度**：通过职责分离和模块化设计，降低代码复杂度
4. **良好可读性**：清晰的代码结构和完整的注释
5. **向后兼容**：保持与现有系统的兼容性

这套监控系统为 topology-aware 策略提供了全面的可观测性，帮助用户更好地理解和优化容器资源分配。
