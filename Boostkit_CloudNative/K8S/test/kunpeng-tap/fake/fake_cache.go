// Code generated by counterfeiter. DO NOT EDIT.
package fake

import (
	"sync"

	"github.com/docker/docker/client"
	v1 "k8s.io/cri-api/pkg/apis/runtime/v1"
	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/cache"
)

type FakeCache struct {
	CleanupStaleContainersStub        func([]string) int
	cleanupStaleContainersMutex       sync.RWMutex
	cleanupStaleContainersArgsForCall []struct {
		arg1 []string
	}
	cleanupStaleContainersReturns struct {
		result1 int
	}
	cleanupStaleContainersReturnsOnCall map[int]struct {
		result1 int
	}
	DeleteContainerStub        func(string) cache.Container
	deleteContainerMutex       sync.RWMutex
	deleteContainerArgsForCall []struct {
		arg1 string
	}
	deleteContainerReturns struct {
		result1 cache.Container
	}
	deleteContainerReturnsOnCall map[int]struct {
		result1 cache.Container
	}
	DeletePodStub        func(string) cache.Pod
	deletePodMutex       sync.RWMutex
	deletePodArgsForCall []struct {
		arg1 string
	}
	deletePodReturns struct {
		result1 cache.Pod
	}
	deletePodReturnsOnCall map[int]struct {
		result1 cache.Pod
	}
	GetContainerCacheIdsStub        func() []string
	getContainerCacheIdsMutex       sync.RWMutex
	getContainerCacheIdsArgsForCall []struct {
	}
	getContainerCacheIdsReturns struct {
		result1 []string
	}
	getContainerCacheIdsReturnsOnCall map[int]struct {
		result1 []string
	}
	GetContainerIdsStub        func() []string
	getContainerIdsMutex       sync.RWMutex
	getContainerIdsArgsForCall []struct {
	}
	getContainerIdsReturns struct {
		result1 []string
	}
	getContainerIdsReturnsOnCall map[int]struct {
		result1 []string
	}
	GetContainersStub        func() []cache.Container
	getContainersMutex       sync.RWMutex
	getContainersArgsForCall []struct {
	}
	getContainersReturns struct {
		result1 []cache.Container
	}
	getContainersReturnsOnCall map[int]struct {
		result1 []cache.Container
	}
	GetNodeResourcesStub        func() []cache.NumaNodeResources
	getNodeResourcesMutex       sync.RWMutex
	getNodeResourcesArgsForCall []struct {
	}
	getNodeResourcesReturns struct {
		result1 []cache.NumaNodeResources
	}
	getNodeResourcesReturnsOnCall map[int]struct {
		result1 []cache.NumaNodeResources
	}
	GetPendingContainersStub        func() []cache.Container
	getPendingContainersMutex       sync.RWMutex
	getPendingContainersArgsForCall []struct {
	}
	getPendingContainersReturns struct {
		result1 []cache.Container
	}
	getPendingContainersReturnsOnCall map[int]struct {
		result1 []cache.Container
	}
	GetPodsStub        func() []cache.Pod
	getPodsMutex       sync.RWMutex
	getPodsArgsForCall []struct {
	}
	getPodsReturns struct {
		result1 []cache.Pod
	}
	getPodsReturnsOnCall map[int]struct {
		result1 []cache.Pod
	}
	InsertContainerStub        func(string, interface{}) (cache.Container, error)
	insertContainerMutex       sync.RWMutex
	insertContainerArgsForCall []struct {
		arg1 string
		arg2 interface{}
	}
	insertContainerReturns struct {
		result1 cache.Container
		result2 error
	}
	insertContainerReturnsOnCall map[int]struct {
		result1 cache.Container
		result2 error
	}
	InsertPodStub        func(string, interface{}, *cache.PodStatus) (cache.Pod, error)
	insertPodMutex       sync.RWMutex
	insertPodArgsForCall []struct {
		arg1 string
		arg2 interface{}
		arg3 *cache.PodStatus
	}
	insertPodReturns struct {
		result1 cache.Pod
		result2 error
	}
	insertPodReturnsOnCall map[int]struct {
		result1 cache.Pod
		result2 error
	}
	LoadStoreContainerdStub        func(v1.RuntimeServiceClient) error
	loadStoreContainerdMutex       sync.RWMutex
	loadStoreContainerdArgsForCall []struct {
		arg1 v1.RuntimeServiceClient
	}
	loadStoreContainerdReturns struct {
		result1 error
	}
	loadStoreContainerdReturnsOnCall map[int]struct {
		result1 error
	}
	LoadStoreDockerStub        func(client.CommonAPIClient, string) error
	loadStoreDockerMutex       sync.RWMutex
	loadStoreDockerArgsForCall []struct {
		arg1 client.CommonAPIClient
		arg2 string
	}
	loadStoreDockerReturns struct {
		result1 error
	}
	loadStoreDockerReturnsOnCall map[int]struct {
		result1 error
	}
	LookupContainerStub        func(string) (cache.Container, bool)
	lookupContainerMutex       sync.RWMutex
	lookupContainerArgsForCall []struct {
		arg1 string
	}
	lookupContainerReturns struct {
		result1 cache.Container
		result2 bool
	}
	lookupContainerReturnsOnCall map[int]struct {
		result1 cache.Container
		result2 bool
	}
	LookupPodStub        func(string) (cache.Pod, bool)
	lookupPodMutex       sync.RWMutex
	lookupPodArgsForCall []struct {
		arg1 string
	}
	lookupPodReturns struct {
		result1 cache.Pod
		result2 bool
	}
	lookupPodReturnsOnCall map[int]struct {
		result1 cache.Pod
		result2 bool
	}
	RefreshContainersStub        func(*v1.ListContainersResponse) ([]cache.Container, []cache.Container)
	refreshContainersMutex       sync.RWMutex
	refreshContainersArgsForCall []struct {
		arg1 *v1.ListContainersResponse
	}
	refreshContainersReturns struct {
		result1 []cache.Container
		result2 []cache.Container
	}
	refreshContainersReturnsOnCall map[int]struct {
		result1 []cache.Container
		result2 []cache.Container
	}
	RefreshPodsStub        func(*v1.ListPodSandboxResponse, map[string]*cache.PodStatus) ([]cache.Pod, []cache.Pod, []cache.Container)
	refreshPodsMutex       sync.RWMutex
	refreshPodsArgsForCall []struct {
		arg1 *v1.ListPodSandboxResponse
		arg2 map[string]*cache.PodStatus
	}
	refreshPodsReturns struct {
		result1 []cache.Pod
		result2 []cache.Pod
		result3 []cache.Container
	}
	refreshPodsReturnsOnCall map[int]struct {
		result1 []cache.Pod
		result2 []cache.Pod
		result3 []cache.Container
	}
	UpdateContainerIDStub        func(string, interface{}) (cache.Container, error)
	updateContainerIDMutex       sync.RWMutex
	updateContainerIDArgsForCall []struct {
		arg1 string
		arg2 interface{}
	}
	updateContainerIDReturns struct {
		result1 cache.Container
		result2 error
	}
	updateContainerIDReturnsOnCall map[int]struct {
		result1 cache.Container
		result2 error
	}
	ValidateCachedContainersStub        func([]string) []string
	validateCachedContainersMutex       sync.RWMutex
	validateCachedContainersArgsForCall []struct {
		arg1 []string
	}
	validateCachedContainersReturns struct {
		result1 []string
	}
	validateCachedContainersReturnsOnCall map[int]struct {
		result1 []string
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeCache) CleanupStaleContainers(arg1 []string) int {
	var arg1Copy []string
	if arg1 != nil {
		arg1Copy = make([]string, len(arg1))
		copy(arg1Copy, arg1)
	}
	fake.cleanupStaleContainersMutex.Lock()
	ret, specificReturn := fake.cleanupStaleContainersReturnsOnCall[len(fake.cleanupStaleContainersArgsForCall)]
	fake.cleanupStaleContainersArgsForCall = append(fake.cleanupStaleContainersArgsForCall, struct {
		arg1 []string
	}{arg1Copy})
	stub := fake.CleanupStaleContainersStub
	fakeReturns := fake.cleanupStaleContainersReturns
	fake.recordInvocation("CleanupStaleContainers", []interface{}{arg1Copy})
	fake.cleanupStaleContainersMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) CleanupStaleContainersCallCount() int {
	fake.cleanupStaleContainersMutex.RLock()
	defer fake.cleanupStaleContainersMutex.RUnlock()
	return len(fake.cleanupStaleContainersArgsForCall)
}

func (fake *FakeCache) CleanupStaleContainersCalls(stub func([]string) int) {
	fake.cleanupStaleContainersMutex.Lock()
	defer fake.cleanupStaleContainersMutex.Unlock()
	fake.CleanupStaleContainersStub = stub
}

func (fake *FakeCache) CleanupStaleContainersArgsForCall(i int) []string {
	fake.cleanupStaleContainersMutex.RLock()
	defer fake.cleanupStaleContainersMutex.RUnlock()
	argsForCall := fake.cleanupStaleContainersArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeCache) CleanupStaleContainersReturns(result1 int) {
	fake.cleanupStaleContainersMutex.Lock()
	defer fake.cleanupStaleContainersMutex.Unlock()
	fake.CleanupStaleContainersStub = nil
	fake.cleanupStaleContainersReturns = struct {
		result1 int
	}{result1}
}

func (fake *FakeCache) CleanupStaleContainersReturnsOnCall(i int, result1 int) {
	fake.cleanupStaleContainersMutex.Lock()
	defer fake.cleanupStaleContainersMutex.Unlock()
	fake.CleanupStaleContainersStub = nil
	if fake.cleanupStaleContainersReturnsOnCall == nil {
		fake.cleanupStaleContainersReturnsOnCall = make(map[int]struct {
			result1 int
		})
	}
	fake.cleanupStaleContainersReturnsOnCall[i] = struct {
		result1 int
	}{result1}
}

func (fake *FakeCache) DeleteContainer(arg1 string) cache.Container {
	fake.deleteContainerMutex.Lock()
	ret, specificReturn := fake.deleteContainerReturnsOnCall[len(fake.deleteContainerArgsForCall)]
	fake.deleteContainerArgsForCall = append(fake.deleteContainerArgsForCall, struct {
		arg1 string
	}{arg1})
	stub := fake.DeleteContainerStub
	fakeReturns := fake.deleteContainerReturns
	fake.recordInvocation("DeleteContainer", []interface{}{arg1})
	fake.deleteContainerMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) DeleteContainerCallCount() int {
	fake.deleteContainerMutex.RLock()
	defer fake.deleteContainerMutex.RUnlock()
	return len(fake.deleteContainerArgsForCall)
}

func (fake *FakeCache) DeleteContainerCalls(stub func(string) cache.Container) {
	fake.deleteContainerMutex.Lock()
	defer fake.deleteContainerMutex.Unlock()
	fake.DeleteContainerStub = stub
}

func (fake *FakeCache) DeleteContainerArgsForCall(i int) string {
	fake.deleteContainerMutex.RLock()
	defer fake.deleteContainerMutex.RUnlock()
	argsForCall := fake.deleteContainerArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeCache) DeleteContainerReturns(result1 cache.Container) {
	fake.deleteContainerMutex.Lock()
	defer fake.deleteContainerMutex.Unlock()
	fake.DeleteContainerStub = nil
	fake.deleteContainerReturns = struct {
		result1 cache.Container
	}{result1}
}

func (fake *FakeCache) DeleteContainerReturnsOnCall(i int, result1 cache.Container) {
	fake.deleteContainerMutex.Lock()
	defer fake.deleteContainerMutex.Unlock()
	fake.DeleteContainerStub = nil
	if fake.deleteContainerReturnsOnCall == nil {
		fake.deleteContainerReturnsOnCall = make(map[int]struct {
			result1 cache.Container
		})
	}
	fake.deleteContainerReturnsOnCall[i] = struct {
		result1 cache.Container
	}{result1}
}

func (fake *FakeCache) DeletePod(arg1 string) cache.Pod {
	fake.deletePodMutex.Lock()
	ret, specificReturn := fake.deletePodReturnsOnCall[len(fake.deletePodArgsForCall)]
	fake.deletePodArgsForCall = append(fake.deletePodArgsForCall, struct {
		arg1 string
	}{arg1})
	stub := fake.DeletePodStub
	fakeReturns := fake.deletePodReturns
	fake.recordInvocation("DeletePod", []interface{}{arg1})
	fake.deletePodMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) DeletePodCallCount() int {
	fake.deletePodMutex.RLock()
	defer fake.deletePodMutex.RUnlock()
	return len(fake.deletePodArgsForCall)
}

func (fake *FakeCache) DeletePodCalls(stub func(string) cache.Pod) {
	fake.deletePodMutex.Lock()
	defer fake.deletePodMutex.Unlock()
	fake.DeletePodStub = stub
}

func (fake *FakeCache) DeletePodArgsForCall(i int) string {
	fake.deletePodMutex.RLock()
	defer fake.deletePodMutex.RUnlock()
	argsForCall := fake.deletePodArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeCache) DeletePodReturns(result1 cache.Pod) {
	fake.deletePodMutex.Lock()
	defer fake.deletePodMutex.Unlock()
	fake.DeletePodStub = nil
	fake.deletePodReturns = struct {
		result1 cache.Pod
	}{result1}
}

func (fake *FakeCache) DeletePodReturnsOnCall(i int, result1 cache.Pod) {
	fake.deletePodMutex.Lock()
	defer fake.deletePodMutex.Unlock()
	fake.DeletePodStub = nil
	if fake.deletePodReturnsOnCall == nil {
		fake.deletePodReturnsOnCall = make(map[int]struct {
			result1 cache.Pod
		})
	}
	fake.deletePodReturnsOnCall[i] = struct {
		result1 cache.Pod
	}{result1}
}

func (fake *FakeCache) GetContainerCacheIds() []string {
	fake.getContainerCacheIdsMutex.Lock()
	ret, specificReturn := fake.getContainerCacheIdsReturnsOnCall[len(fake.getContainerCacheIdsArgsForCall)]
	fake.getContainerCacheIdsArgsForCall = append(fake.getContainerCacheIdsArgsForCall, struct {
	}{})
	stub := fake.GetContainerCacheIdsStub
	fakeReturns := fake.getContainerCacheIdsReturns
	fake.recordInvocation("GetContainerCacheIds", []interface{}{})
	fake.getContainerCacheIdsMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) GetContainerCacheIdsCallCount() int {
	fake.getContainerCacheIdsMutex.RLock()
	defer fake.getContainerCacheIdsMutex.RUnlock()
	return len(fake.getContainerCacheIdsArgsForCall)
}

func (fake *FakeCache) GetContainerCacheIdsCalls(stub func() []string) {
	fake.getContainerCacheIdsMutex.Lock()
	defer fake.getContainerCacheIdsMutex.Unlock()
	fake.GetContainerCacheIdsStub = stub
}

func (fake *FakeCache) GetContainerCacheIdsReturns(result1 []string) {
	fake.getContainerCacheIdsMutex.Lock()
	defer fake.getContainerCacheIdsMutex.Unlock()
	fake.GetContainerCacheIdsStub = nil
	fake.getContainerCacheIdsReturns = struct {
		result1 []string
	}{result1}
}

func (fake *FakeCache) GetContainerCacheIdsReturnsOnCall(i int, result1 []string) {
	fake.getContainerCacheIdsMutex.Lock()
	defer fake.getContainerCacheIdsMutex.Unlock()
	fake.GetContainerCacheIdsStub = nil
	if fake.getContainerCacheIdsReturnsOnCall == nil {
		fake.getContainerCacheIdsReturnsOnCall = make(map[int]struct {
			result1 []string
		})
	}
	fake.getContainerCacheIdsReturnsOnCall[i] = struct {
		result1 []string
	}{result1}
}

func (fake *FakeCache) GetContainerIds() []string {
	fake.getContainerIdsMutex.Lock()
	ret, specificReturn := fake.getContainerIdsReturnsOnCall[len(fake.getContainerIdsArgsForCall)]
	fake.getContainerIdsArgsForCall = append(fake.getContainerIdsArgsForCall, struct {
	}{})
	stub := fake.GetContainerIdsStub
	fakeReturns := fake.getContainerIdsReturns
	fake.recordInvocation("GetContainerIds", []interface{}{})
	fake.getContainerIdsMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) GetContainerIdsCallCount() int {
	fake.getContainerIdsMutex.RLock()
	defer fake.getContainerIdsMutex.RUnlock()
	return len(fake.getContainerIdsArgsForCall)
}

func (fake *FakeCache) GetContainerIdsCalls(stub func() []string) {
	fake.getContainerIdsMutex.Lock()
	defer fake.getContainerIdsMutex.Unlock()
	fake.GetContainerIdsStub = stub
}

func (fake *FakeCache) GetContainerIdsReturns(result1 []string) {
	fake.getContainerIdsMutex.Lock()
	defer fake.getContainerIdsMutex.Unlock()
	fake.GetContainerIdsStub = nil
	fake.getContainerIdsReturns = struct {
		result1 []string
	}{result1}
}

func (fake *FakeCache) GetContainerIdsReturnsOnCall(i int, result1 []string) {
	fake.getContainerIdsMutex.Lock()
	defer fake.getContainerIdsMutex.Unlock()
	fake.GetContainerIdsStub = nil
	if fake.getContainerIdsReturnsOnCall == nil {
		fake.getContainerIdsReturnsOnCall = make(map[int]struct {
			result1 []string
		})
	}
	fake.getContainerIdsReturnsOnCall[i] = struct {
		result1 []string
	}{result1}
}

func (fake *FakeCache) GetContainers() []cache.Container {
	fake.getContainersMutex.Lock()
	ret, specificReturn := fake.getContainersReturnsOnCall[len(fake.getContainersArgsForCall)]
	fake.getContainersArgsForCall = append(fake.getContainersArgsForCall, struct {
	}{})
	stub := fake.GetContainersStub
	fakeReturns := fake.getContainersReturns
	fake.recordInvocation("GetContainers", []interface{}{})
	fake.getContainersMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) GetContainersCallCount() int {
	fake.getContainersMutex.RLock()
	defer fake.getContainersMutex.RUnlock()
	return len(fake.getContainersArgsForCall)
}

func (fake *FakeCache) GetContainersCalls(stub func() []cache.Container) {
	fake.getContainersMutex.Lock()
	defer fake.getContainersMutex.Unlock()
	fake.GetContainersStub = stub
}

func (fake *FakeCache) GetContainersReturns(result1 []cache.Container) {
	fake.getContainersMutex.Lock()
	defer fake.getContainersMutex.Unlock()
	fake.GetContainersStub = nil
	fake.getContainersReturns = struct {
		result1 []cache.Container
	}{result1}
}

func (fake *FakeCache) GetContainersReturnsOnCall(i int, result1 []cache.Container) {
	fake.getContainersMutex.Lock()
	defer fake.getContainersMutex.Unlock()
	fake.GetContainersStub = nil
	if fake.getContainersReturnsOnCall == nil {
		fake.getContainersReturnsOnCall = make(map[int]struct {
			result1 []cache.Container
		})
	}
	fake.getContainersReturnsOnCall[i] = struct {
		result1 []cache.Container
	}{result1}
}

func (fake *FakeCache) GetNodeResources() []cache.NumaNodeResources {
	fake.getNodeResourcesMutex.Lock()
	ret, specificReturn := fake.getNodeResourcesReturnsOnCall[len(fake.getNodeResourcesArgsForCall)]
	fake.getNodeResourcesArgsForCall = append(fake.getNodeResourcesArgsForCall, struct {
	}{})
	stub := fake.GetNodeResourcesStub
	fakeReturns := fake.getNodeResourcesReturns
	fake.recordInvocation("GetNodeResources", []interface{}{})
	fake.getNodeResourcesMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) GetNodeResourcesCallCount() int {
	fake.getNodeResourcesMutex.RLock()
	defer fake.getNodeResourcesMutex.RUnlock()
	return len(fake.getNodeResourcesArgsForCall)
}

func (fake *FakeCache) GetNodeResourcesCalls(stub func() []cache.NumaNodeResources) {
	fake.getNodeResourcesMutex.Lock()
	defer fake.getNodeResourcesMutex.Unlock()
	fake.GetNodeResourcesStub = stub
}

func (fake *FakeCache) GetNodeResourcesReturns(result1 []cache.NumaNodeResources) {
	fake.getNodeResourcesMutex.Lock()
	defer fake.getNodeResourcesMutex.Unlock()
	fake.GetNodeResourcesStub = nil
	fake.getNodeResourcesReturns = struct {
		result1 []cache.NumaNodeResources
	}{result1}
}

func (fake *FakeCache) GetNodeResourcesReturnsOnCall(i int, result1 []cache.NumaNodeResources) {
	fake.getNodeResourcesMutex.Lock()
	defer fake.getNodeResourcesMutex.Unlock()
	fake.GetNodeResourcesStub = nil
	if fake.getNodeResourcesReturnsOnCall == nil {
		fake.getNodeResourcesReturnsOnCall = make(map[int]struct {
			result1 []cache.NumaNodeResources
		})
	}
	fake.getNodeResourcesReturnsOnCall[i] = struct {
		result1 []cache.NumaNodeResources
	}{result1}
}

func (fake *FakeCache) GetPendingContainers() []cache.Container {
	fake.getPendingContainersMutex.Lock()
	ret, specificReturn := fake.getPendingContainersReturnsOnCall[len(fake.getPendingContainersArgsForCall)]
	fake.getPendingContainersArgsForCall = append(fake.getPendingContainersArgsForCall, struct {
	}{})
	stub := fake.GetPendingContainersStub
	fakeReturns := fake.getPendingContainersReturns
	fake.recordInvocation("GetPendingContainers", []interface{}{})
	fake.getPendingContainersMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) GetPendingContainersCallCount() int {
	fake.getPendingContainersMutex.RLock()
	defer fake.getPendingContainersMutex.RUnlock()
	return len(fake.getPendingContainersArgsForCall)
}

func (fake *FakeCache) GetPendingContainersCalls(stub func() []cache.Container) {
	fake.getPendingContainersMutex.Lock()
	defer fake.getPendingContainersMutex.Unlock()
	fake.GetPendingContainersStub = stub
}

func (fake *FakeCache) GetPendingContainersReturns(result1 []cache.Container) {
	fake.getPendingContainersMutex.Lock()
	defer fake.getPendingContainersMutex.Unlock()
	fake.GetPendingContainersStub = nil
	fake.getPendingContainersReturns = struct {
		result1 []cache.Container
	}{result1}
}

func (fake *FakeCache) GetPendingContainersReturnsOnCall(i int, result1 []cache.Container) {
	fake.getPendingContainersMutex.Lock()
	defer fake.getPendingContainersMutex.Unlock()
	fake.GetPendingContainersStub = nil
	if fake.getPendingContainersReturnsOnCall == nil {
		fake.getPendingContainersReturnsOnCall = make(map[int]struct {
			result1 []cache.Container
		})
	}
	fake.getPendingContainersReturnsOnCall[i] = struct {
		result1 []cache.Container
	}{result1}
}

func (fake *FakeCache) GetPods() []cache.Pod {
	fake.getPodsMutex.Lock()
	ret, specificReturn := fake.getPodsReturnsOnCall[len(fake.getPodsArgsForCall)]
	fake.getPodsArgsForCall = append(fake.getPodsArgsForCall, struct {
	}{})
	stub := fake.GetPodsStub
	fakeReturns := fake.getPodsReturns
	fake.recordInvocation("GetPods", []interface{}{})
	fake.getPodsMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) GetPodsCallCount() int {
	fake.getPodsMutex.RLock()
	defer fake.getPodsMutex.RUnlock()
	return len(fake.getPodsArgsForCall)
}

func (fake *FakeCache) GetPodsCalls(stub func() []cache.Pod) {
	fake.getPodsMutex.Lock()
	defer fake.getPodsMutex.Unlock()
	fake.GetPodsStub = stub
}

func (fake *FakeCache) GetPodsReturns(result1 []cache.Pod) {
	fake.getPodsMutex.Lock()
	defer fake.getPodsMutex.Unlock()
	fake.GetPodsStub = nil
	fake.getPodsReturns = struct {
		result1 []cache.Pod
	}{result1}
}

func (fake *FakeCache) GetPodsReturnsOnCall(i int, result1 []cache.Pod) {
	fake.getPodsMutex.Lock()
	defer fake.getPodsMutex.Unlock()
	fake.GetPodsStub = nil
	if fake.getPodsReturnsOnCall == nil {
		fake.getPodsReturnsOnCall = make(map[int]struct {
			result1 []cache.Pod
		})
	}
	fake.getPodsReturnsOnCall[i] = struct {
		result1 []cache.Pod
	}{result1}
}

func (fake *FakeCache) InsertContainer(arg1 string, arg2 interface{}) (cache.Container, error) {
	fake.insertContainerMutex.Lock()
	ret, specificReturn := fake.insertContainerReturnsOnCall[len(fake.insertContainerArgsForCall)]
	fake.insertContainerArgsForCall = append(fake.insertContainerArgsForCall, struct {
		arg1 string
		arg2 interface{}
	}{arg1, arg2})
	stub := fake.InsertContainerStub
	fakeReturns := fake.insertContainerReturns
	fake.recordInvocation("InsertContainer", []interface{}{arg1, arg2})
	fake.insertContainerMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeCache) InsertContainerCallCount() int {
	fake.insertContainerMutex.RLock()
	defer fake.insertContainerMutex.RUnlock()
	return len(fake.insertContainerArgsForCall)
}

func (fake *FakeCache) InsertContainerCalls(stub func(string, interface{}) (cache.Container, error)) {
	fake.insertContainerMutex.Lock()
	defer fake.insertContainerMutex.Unlock()
	fake.InsertContainerStub = stub
}

func (fake *FakeCache) InsertContainerArgsForCall(i int) (string, interface{}) {
	fake.insertContainerMutex.RLock()
	defer fake.insertContainerMutex.RUnlock()
	argsForCall := fake.insertContainerArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeCache) InsertContainerReturns(result1 cache.Container, result2 error) {
	fake.insertContainerMutex.Lock()
	defer fake.insertContainerMutex.Unlock()
	fake.InsertContainerStub = nil
	fake.insertContainerReturns = struct {
		result1 cache.Container
		result2 error
	}{result1, result2}
}

func (fake *FakeCache) InsertContainerReturnsOnCall(i int, result1 cache.Container, result2 error) {
	fake.insertContainerMutex.Lock()
	defer fake.insertContainerMutex.Unlock()
	fake.InsertContainerStub = nil
	if fake.insertContainerReturnsOnCall == nil {
		fake.insertContainerReturnsOnCall = make(map[int]struct {
			result1 cache.Container
			result2 error
		})
	}
	fake.insertContainerReturnsOnCall[i] = struct {
		result1 cache.Container
		result2 error
	}{result1, result2}
}

func (fake *FakeCache) InsertPod(arg1 string, arg2 interface{}, arg3 *cache.PodStatus) (cache.Pod, error) {
	fake.insertPodMutex.Lock()
	ret, specificReturn := fake.insertPodReturnsOnCall[len(fake.insertPodArgsForCall)]
	fake.insertPodArgsForCall = append(fake.insertPodArgsForCall, struct {
		arg1 string
		arg2 interface{}
		arg3 *cache.PodStatus
	}{arg1, arg2, arg3})
	stub := fake.InsertPodStub
	fakeReturns := fake.insertPodReturns
	fake.recordInvocation("InsertPod", []interface{}{arg1, arg2, arg3})
	fake.insertPodMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeCache) InsertPodCallCount() int {
	fake.insertPodMutex.RLock()
	defer fake.insertPodMutex.RUnlock()
	return len(fake.insertPodArgsForCall)
}

func (fake *FakeCache) InsertPodCalls(stub func(string, interface{}, *cache.PodStatus) (cache.Pod, error)) {
	fake.insertPodMutex.Lock()
	defer fake.insertPodMutex.Unlock()
	fake.InsertPodStub = stub
}

func (fake *FakeCache) InsertPodArgsForCall(i int) (string, interface{}, *cache.PodStatus) {
	fake.insertPodMutex.RLock()
	defer fake.insertPodMutex.RUnlock()
	argsForCall := fake.insertPodArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeCache) InsertPodReturns(result1 cache.Pod, result2 error) {
	fake.insertPodMutex.Lock()
	defer fake.insertPodMutex.Unlock()
	fake.InsertPodStub = nil
	fake.insertPodReturns = struct {
		result1 cache.Pod
		result2 error
	}{result1, result2}
}

func (fake *FakeCache) InsertPodReturnsOnCall(i int, result1 cache.Pod, result2 error) {
	fake.insertPodMutex.Lock()
	defer fake.insertPodMutex.Unlock()
	fake.InsertPodStub = nil
	if fake.insertPodReturnsOnCall == nil {
		fake.insertPodReturnsOnCall = make(map[int]struct {
			result1 cache.Pod
			result2 error
		})
	}
	fake.insertPodReturnsOnCall[i] = struct {
		result1 cache.Pod
		result2 error
	}{result1, result2}
}

func (fake *FakeCache) LoadStoreContainerd(arg1 v1.RuntimeServiceClient) error {
	fake.loadStoreContainerdMutex.Lock()
	ret, specificReturn := fake.loadStoreContainerdReturnsOnCall[len(fake.loadStoreContainerdArgsForCall)]
	fake.loadStoreContainerdArgsForCall = append(fake.loadStoreContainerdArgsForCall, struct {
		arg1 v1.RuntimeServiceClient
	}{arg1})
	stub := fake.LoadStoreContainerdStub
	fakeReturns := fake.loadStoreContainerdReturns
	fake.recordInvocation("LoadStoreContainerd", []interface{}{arg1})
	fake.loadStoreContainerdMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) LoadStoreContainerdCallCount() int {
	fake.loadStoreContainerdMutex.RLock()
	defer fake.loadStoreContainerdMutex.RUnlock()
	return len(fake.loadStoreContainerdArgsForCall)
}

func (fake *FakeCache) LoadStoreContainerdCalls(stub func(v1.RuntimeServiceClient) error) {
	fake.loadStoreContainerdMutex.Lock()
	defer fake.loadStoreContainerdMutex.Unlock()
	fake.LoadStoreContainerdStub = stub
}

func (fake *FakeCache) LoadStoreContainerdArgsForCall(i int) v1.RuntimeServiceClient {
	fake.loadStoreContainerdMutex.RLock()
	defer fake.loadStoreContainerdMutex.RUnlock()
	argsForCall := fake.loadStoreContainerdArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeCache) LoadStoreContainerdReturns(result1 error) {
	fake.loadStoreContainerdMutex.Lock()
	defer fake.loadStoreContainerdMutex.Unlock()
	fake.LoadStoreContainerdStub = nil
	fake.loadStoreContainerdReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeCache) LoadStoreContainerdReturnsOnCall(i int, result1 error) {
	fake.loadStoreContainerdMutex.Lock()
	defer fake.loadStoreContainerdMutex.Unlock()
	fake.LoadStoreContainerdStub = nil
	if fake.loadStoreContainerdReturnsOnCall == nil {
		fake.loadStoreContainerdReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.loadStoreContainerdReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeCache) LoadStoreDocker(arg1 client.CommonAPIClient, arg2 string) error {
	fake.loadStoreDockerMutex.Lock()
	ret, specificReturn := fake.loadStoreDockerReturnsOnCall[len(fake.loadStoreDockerArgsForCall)]
	fake.loadStoreDockerArgsForCall = append(fake.loadStoreDockerArgsForCall, struct {
		arg1 client.CommonAPIClient
		arg2 string
	}{arg1, arg2})
	stub := fake.LoadStoreDockerStub
	fakeReturns := fake.loadStoreDockerReturns
	fake.recordInvocation("LoadStoreDocker", []interface{}{arg1, arg2})
	fake.loadStoreDockerMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) LoadStoreDockerCallCount() int {
	fake.loadStoreDockerMutex.RLock()
	defer fake.loadStoreDockerMutex.RUnlock()
	return len(fake.loadStoreDockerArgsForCall)
}

func (fake *FakeCache) LoadStoreDockerCalls(stub func(client.CommonAPIClient, string) error) {
	fake.loadStoreDockerMutex.Lock()
	defer fake.loadStoreDockerMutex.Unlock()
	fake.LoadStoreDockerStub = stub
}

func (fake *FakeCache) LoadStoreDockerArgsForCall(i int) (client.CommonAPIClient, string) {
	fake.loadStoreDockerMutex.RLock()
	defer fake.loadStoreDockerMutex.RUnlock()
	argsForCall := fake.loadStoreDockerArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeCache) LoadStoreDockerReturns(result1 error) {
	fake.loadStoreDockerMutex.Lock()
	defer fake.loadStoreDockerMutex.Unlock()
	fake.LoadStoreDockerStub = nil
	fake.loadStoreDockerReturns = struct {
		result1 error
	}{result1}
}

func (fake *FakeCache) LoadStoreDockerReturnsOnCall(i int, result1 error) {
	fake.loadStoreDockerMutex.Lock()
	defer fake.loadStoreDockerMutex.Unlock()
	fake.LoadStoreDockerStub = nil
	if fake.loadStoreDockerReturnsOnCall == nil {
		fake.loadStoreDockerReturnsOnCall = make(map[int]struct {
			result1 error
		})
	}
	fake.loadStoreDockerReturnsOnCall[i] = struct {
		result1 error
	}{result1}
}

func (fake *FakeCache) LookupContainer(arg1 string) (cache.Container, bool) {
	fake.lookupContainerMutex.Lock()
	ret, specificReturn := fake.lookupContainerReturnsOnCall[len(fake.lookupContainerArgsForCall)]
	fake.lookupContainerArgsForCall = append(fake.lookupContainerArgsForCall, struct {
		arg1 string
	}{arg1})
	stub := fake.LookupContainerStub
	fakeReturns := fake.lookupContainerReturns
	fake.recordInvocation("LookupContainer", []interface{}{arg1})
	fake.lookupContainerMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeCache) LookupContainerCallCount() int {
	fake.lookupContainerMutex.RLock()
	defer fake.lookupContainerMutex.RUnlock()
	return len(fake.lookupContainerArgsForCall)
}

func (fake *FakeCache) LookupContainerCalls(stub func(string) (cache.Container, bool)) {
	fake.lookupContainerMutex.Lock()
	defer fake.lookupContainerMutex.Unlock()
	fake.LookupContainerStub = stub
}

func (fake *FakeCache) LookupContainerArgsForCall(i int) string {
	fake.lookupContainerMutex.RLock()
	defer fake.lookupContainerMutex.RUnlock()
	argsForCall := fake.lookupContainerArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeCache) LookupContainerReturns(result1 cache.Container, result2 bool) {
	fake.lookupContainerMutex.Lock()
	defer fake.lookupContainerMutex.Unlock()
	fake.LookupContainerStub = nil
	fake.lookupContainerReturns = struct {
		result1 cache.Container
		result2 bool
	}{result1, result2}
}

func (fake *FakeCache) LookupContainerReturnsOnCall(i int, result1 cache.Container, result2 bool) {
	fake.lookupContainerMutex.Lock()
	defer fake.lookupContainerMutex.Unlock()
	fake.LookupContainerStub = nil
	if fake.lookupContainerReturnsOnCall == nil {
		fake.lookupContainerReturnsOnCall = make(map[int]struct {
			result1 cache.Container
			result2 bool
		})
	}
	fake.lookupContainerReturnsOnCall[i] = struct {
		result1 cache.Container
		result2 bool
	}{result1, result2}
}

func (fake *FakeCache) LookupPod(arg1 string) (cache.Pod, bool) {
	fake.lookupPodMutex.Lock()
	ret, specificReturn := fake.lookupPodReturnsOnCall[len(fake.lookupPodArgsForCall)]
	fake.lookupPodArgsForCall = append(fake.lookupPodArgsForCall, struct {
		arg1 string
	}{arg1})
	stub := fake.LookupPodStub
	fakeReturns := fake.lookupPodReturns
	fake.recordInvocation("LookupPod", []interface{}{arg1})
	fake.lookupPodMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeCache) LookupPodCallCount() int {
	fake.lookupPodMutex.RLock()
	defer fake.lookupPodMutex.RUnlock()
	return len(fake.lookupPodArgsForCall)
}

func (fake *FakeCache) LookupPodCalls(stub func(string) (cache.Pod, bool)) {
	fake.lookupPodMutex.Lock()
	defer fake.lookupPodMutex.Unlock()
	fake.LookupPodStub = stub
}

func (fake *FakeCache) LookupPodArgsForCall(i int) string {
	fake.lookupPodMutex.RLock()
	defer fake.lookupPodMutex.RUnlock()
	argsForCall := fake.lookupPodArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeCache) LookupPodReturns(result1 cache.Pod, result2 bool) {
	fake.lookupPodMutex.Lock()
	defer fake.lookupPodMutex.Unlock()
	fake.LookupPodStub = nil
	fake.lookupPodReturns = struct {
		result1 cache.Pod
		result2 bool
	}{result1, result2}
}

func (fake *FakeCache) LookupPodReturnsOnCall(i int, result1 cache.Pod, result2 bool) {
	fake.lookupPodMutex.Lock()
	defer fake.lookupPodMutex.Unlock()
	fake.LookupPodStub = nil
	if fake.lookupPodReturnsOnCall == nil {
		fake.lookupPodReturnsOnCall = make(map[int]struct {
			result1 cache.Pod
			result2 bool
		})
	}
	fake.lookupPodReturnsOnCall[i] = struct {
		result1 cache.Pod
		result2 bool
	}{result1, result2}
}

func (fake *FakeCache) RefreshContainers(arg1 *v1.ListContainersResponse) ([]cache.Container, []cache.Container) {
	fake.refreshContainersMutex.Lock()
	ret, specificReturn := fake.refreshContainersReturnsOnCall[len(fake.refreshContainersArgsForCall)]
	fake.refreshContainersArgsForCall = append(fake.refreshContainersArgsForCall, struct {
		arg1 *v1.ListContainersResponse
	}{arg1})
	stub := fake.RefreshContainersStub
	fakeReturns := fake.refreshContainersReturns
	fake.recordInvocation("RefreshContainers", []interface{}{arg1})
	fake.refreshContainersMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeCache) RefreshContainersCallCount() int {
	fake.refreshContainersMutex.RLock()
	defer fake.refreshContainersMutex.RUnlock()
	return len(fake.refreshContainersArgsForCall)
}

func (fake *FakeCache) RefreshContainersCalls(stub func(*v1.ListContainersResponse) ([]cache.Container, []cache.Container)) {
	fake.refreshContainersMutex.Lock()
	defer fake.refreshContainersMutex.Unlock()
	fake.RefreshContainersStub = stub
}

func (fake *FakeCache) RefreshContainersArgsForCall(i int) *v1.ListContainersResponse {
	fake.refreshContainersMutex.RLock()
	defer fake.refreshContainersMutex.RUnlock()
	argsForCall := fake.refreshContainersArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeCache) RefreshContainersReturns(result1 []cache.Container, result2 []cache.Container) {
	fake.refreshContainersMutex.Lock()
	defer fake.refreshContainersMutex.Unlock()
	fake.RefreshContainersStub = nil
	fake.refreshContainersReturns = struct {
		result1 []cache.Container
		result2 []cache.Container
	}{result1, result2}
}

func (fake *FakeCache) RefreshContainersReturnsOnCall(i int, result1 []cache.Container, result2 []cache.Container) {
	fake.refreshContainersMutex.Lock()
	defer fake.refreshContainersMutex.Unlock()
	fake.RefreshContainersStub = nil
	if fake.refreshContainersReturnsOnCall == nil {
		fake.refreshContainersReturnsOnCall = make(map[int]struct {
			result1 []cache.Container
			result2 []cache.Container
		})
	}
	fake.refreshContainersReturnsOnCall[i] = struct {
		result1 []cache.Container
		result2 []cache.Container
	}{result1, result2}
}

func (fake *FakeCache) RefreshPods(arg1 *v1.ListPodSandboxResponse, arg2 map[string]*cache.PodStatus) ([]cache.Pod, []cache.Pod, []cache.Container) {
	fake.refreshPodsMutex.Lock()
	ret, specificReturn := fake.refreshPodsReturnsOnCall[len(fake.refreshPodsArgsForCall)]
	fake.refreshPodsArgsForCall = append(fake.refreshPodsArgsForCall, struct {
		arg1 *v1.ListPodSandboxResponse
		arg2 map[string]*cache.PodStatus
	}{arg1, arg2})
	stub := fake.RefreshPodsStub
	fakeReturns := fake.refreshPodsReturns
	fake.recordInvocation("RefreshPods", []interface{}{arg1, arg2})
	fake.refreshPodsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1, ret.result2, ret.result3
	}
	return fakeReturns.result1, fakeReturns.result2, fakeReturns.result3
}

func (fake *FakeCache) RefreshPodsCallCount() int {
	fake.refreshPodsMutex.RLock()
	defer fake.refreshPodsMutex.RUnlock()
	return len(fake.refreshPodsArgsForCall)
}

func (fake *FakeCache) RefreshPodsCalls(stub func(*v1.ListPodSandboxResponse, map[string]*cache.PodStatus) ([]cache.Pod, []cache.Pod, []cache.Container)) {
	fake.refreshPodsMutex.Lock()
	defer fake.refreshPodsMutex.Unlock()
	fake.RefreshPodsStub = stub
}

func (fake *FakeCache) RefreshPodsArgsForCall(i int) (*v1.ListPodSandboxResponse, map[string]*cache.PodStatus) {
	fake.refreshPodsMutex.RLock()
	defer fake.refreshPodsMutex.RUnlock()
	argsForCall := fake.refreshPodsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeCache) RefreshPodsReturns(result1 []cache.Pod, result2 []cache.Pod, result3 []cache.Container) {
	fake.refreshPodsMutex.Lock()
	defer fake.refreshPodsMutex.Unlock()
	fake.RefreshPodsStub = nil
	fake.refreshPodsReturns = struct {
		result1 []cache.Pod
		result2 []cache.Pod
		result3 []cache.Container
	}{result1, result2, result3}
}

func (fake *FakeCache) RefreshPodsReturnsOnCall(i int, result1 []cache.Pod, result2 []cache.Pod, result3 []cache.Container) {
	fake.refreshPodsMutex.Lock()
	defer fake.refreshPodsMutex.Unlock()
	fake.RefreshPodsStub = nil
	if fake.refreshPodsReturnsOnCall == nil {
		fake.refreshPodsReturnsOnCall = make(map[int]struct {
			result1 []cache.Pod
			result2 []cache.Pod
			result3 []cache.Container
		})
	}
	fake.refreshPodsReturnsOnCall[i] = struct {
		result1 []cache.Pod
		result2 []cache.Pod
		result3 []cache.Container
	}{result1, result2, result3}
}

func (fake *FakeCache) UpdateContainerID(arg1 string, arg2 interface{}) (cache.Container, error) {
	fake.updateContainerIDMutex.Lock()
	ret, specificReturn := fake.updateContainerIDReturnsOnCall[len(fake.updateContainerIDArgsForCall)]
	fake.updateContainerIDArgsForCall = append(fake.updateContainerIDArgsForCall, struct {
		arg1 string
		arg2 interface{}
	}{arg1, arg2})
	stub := fake.UpdateContainerIDStub
	fakeReturns := fake.updateContainerIDReturns
	fake.recordInvocation("UpdateContainerID", []interface{}{arg1, arg2})
	fake.updateContainerIDMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeCache) UpdateContainerIDCallCount() int {
	fake.updateContainerIDMutex.RLock()
	defer fake.updateContainerIDMutex.RUnlock()
	return len(fake.updateContainerIDArgsForCall)
}

func (fake *FakeCache) UpdateContainerIDCalls(stub func(string, interface{}) (cache.Container, error)) {
	fake.updateContainerIDMutex.Lock()
	defer fake.updateContainerIDMutex.Unlock()
	fake.UpdateContainerIDStub = stub
}

func (fake *FakeCache) UpdateContainerIDArgsForCall(i int) (string, interface{}) {
	fake.updateContainerIDMutex.RLock()
	defer fake.updateContainerIDMutex.RUnlock()
	argsForCall := fake.updateContainerIDArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeCache) UpdateContainerIDReturns(result1 cache.Container, result2 error) {
	fake.updateContainerIDMutex.Lock()
	defer fake.updateContainerIDMutex.Unlock()
	fake.UpdateContainerIDStub = nil
	fake.updateContainerIDReturns = struct {
		result1 cache.Container
		result2 error
	}{result1, result2}
}

func (fake *FakeCache) UpdateContainerIDReturnsOnCall(i int, result1 cache.Container, result2 error) {
	fake.updateContainerIDMutex.Lock()
	defer fake.updateContainerIDMutex.Unlock()
	fake.UpdateContainerIDStub = nil
	if fake.updateContainerIDReturnsOnCall == nil {
		fake.updateContainerIDReturnsOnCall = make(map[int]struct {
			result1 cache.Container
			result2 error
		})
	}
	fake.updateContainerIDReturnsOnCall[i] = struct {
		result1 cache.Container
		result2 error
	}{result1, result2}
}

func (fake *FakeCache) ValidateCachedContainers(arg1 []string) []string {
	var arg1Copy []string
	if arg1 != nil {
		arg1Copy = make([]string, len(arg1))
		copy(arg1Copy, arg1)
	}
	fake.validateCachedContainersMutex.Lock()
	ret, specificReturn := fake.validateCachedContainersReturnsOnCall[len(fake.validateCachedContainersArgsForCall)]
	fake.validateCachedContainersArgsForCall = append(fake.validateCachedContainersArgsForCall, struct {
		arg1 []string
	}{arg1Copy})
	stub := fake.ValidateCachedContainersStub
	fakeReturns := fake.validateCachedContainersReturns
	fake.recordInvocation("ValidateCachedContainers", []interface{}{arg1Copy})
	fake.validateCachedContainersMutex.Unlock()
	if stub != nil {
		return stub(arg1)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeCache) ValidateCachedContainersCallCount() int {
	fake.validateCachedContainersMutex.RLock()
	defer fake.validateCachedContainersMutex.RUnlock()
	return len(fake.validateCachedContainersArgsForCall)
}

func (fake *FakeCache) ValidateCachedContainersCalls(stub func([]string) []string) {
	fake.validateCachedContainersMutex.Lock()
	defer fake.validateCachedContainersMutex.Unlock()
	fake.ValidateCachedContainersStub = stub
}

func (fake *FakeCache) ValidateCachedContainersArgsForCall(i int) []string {
	fake.validateCachedContainersMutex.RLock()
	defer fake.validateCachedContainersMutex.RUnlock()
	argsForCall := fake.validateCachedContainersArgsForCall[i]
	return argsForCall.arg1
}

func (fake *FakeCache) ValidateCachedContainersReturns(result1 []string) {
	fake.validateCachedContainersMutex.Lock()
	defer fake.validateCachedContainersMutex.Unlock()
	fake.ValidateCachedContainersStub = nil
	fake.validateCachedContainersReturns = struct {
		result1 []string
	}{result1}
}

func (fake *FakeCache) ValidateCachedContainersReturnsOnCall(i int, result1 []string) {
	fake.validateCachedContainersMutex.Lock()
	defer fake.validateCachedContainersMutex.Unlock()
	fake.ValidateCachedContainersStub = nil
	if fake.validateCachedContainersReturnsOnCall == nil {
		fake.validateCachedContainersReturnsOnCall = make(map[int]struct {
			result1 []string
		})
	}
	fake.validateCachedContainersReturnsOnCall[i] = struct {
		result1 []string
	}{result1}
}

func (fake *FakeCache) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.cleanupStaleContainersMutex.RLock()
	defer fake.cleanupStaleContainersMutex.RUnlock()
	fake.deleteContainerMutex.RLock()
	defer fake.deleteContainerMutex.RUnlock()
	fake.deletePodMutex.RLock()
	defer fake.deletePodMutex.RUnlock()
	fake.getContainerCacheIdsMutex.RLock()
	defer fake.getContainerCacheIdsMutex.RUnlock()
	fake.getContainerIdsMutex.RLock()
	defer fake.getContainerIdsMutex.RUnlock()
	fake.getContainersMutex.RLock()
	defer fake.getContainersMutex.RUnlock()
	fake.getNodeResourcesMutex.RLock()
	defer fake.getNodeResourcesMutex.RUnlock()
	fake.getPendingContainersMutex.RLock()
	defer fake.getPendingContainersMutex.RUnlock()
	fake.getPodsMutex.RLock()
	defer fake.getPodsMutex.RUnlock()
	fake.insertContainerMutex.RLock()
	defer fake.insertContainerMutex.RUnlock()
	fake.insertPodMutex.RLock()
	defer fake.insertPodMutex.RUnlock()
	fake.loadStoreContainerdMutex.RLock()
	defer fake.loadStoreContainerdMutex.RUnlock()
	fake.loadStoreDockerMutex.RLock()
	defer fake.loadStoreDockerMutex.RUnlock()
	fake.lookupContainerMutex.RLock()
	defer fake.lookupContainerMutex.RUnlock()
	fake.lookupPodMutex.RLock()
	defer fake.lookupPodMutex.RUnlock()
	fake.refreshContainersMutex.RLock()
	defer fake.refreshContainersMutex.RUnlock()
	fake.refreshPodsMutex.RLock()
	defer fake.refreshPodsMutex.RUnlock()
	fake.updateContainerIDMutex.RLock()
	defer fake.updateContainerIDMutex.RUnlock()
	fake.validateCachedContainersMutex.RLock()
	defer fake.validateCachedContainersMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeCache) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ cache.Cache = new(FakeCache)
