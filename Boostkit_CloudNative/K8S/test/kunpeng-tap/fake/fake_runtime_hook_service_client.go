// Code generated by counterfeiter. DO NOT EDIT.
package fake

import (
	"context"
	"sync"

	"google.golang.org/grpc"
	"kunpeng.huawei.com/kunpeng-cloud-computing/api/kunpeng-tap/policy-manager/v1alpha1"
)

type FakeRuntimeHookServiceClient struct {
	PostStartContainerHookStub        func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)
	postStartContainerHookMutex       sync.RWMutex
	postStartContainerHookArgsForCall []struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}
	postStartContainerHookReturns struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	postStartContainerHookReturnsOnCall map[int]struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	PostStopContainerHookStub        func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)
	postStopContainerHookMutex       sync.RWMutex
	postStopContainerHookArgsForCall []struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}
	postStopContainerHookReturns struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	postStopContainerHookReturnsOnCall map[int]struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	PostStopPodSandboxHookStub        func(context.Context, *v1alpha1.PodSandboxHookRequest, ...grpc.CallOption) (*v1alpha1.PodSandboxHookResponse, error)
	postStopPodSandboxHookMutex       sync.RWMutex
	postStopPodSandboxHookArgsForCall []struct {
		arg1 context.Context
		arg2 *v1alpha1.PodSandboxHookRequest
		arg3 []grpc.CallOption
	}
	postStopPodSandboxHookReturns struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}
	postStopPodSandboxHookReturnsOnCall map[int]struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}
	PreCreateContainerHookStub        func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)
	preCreateContainerHookMutex       sync.RWMutex
	preCreateContainerHookArgsForCall []struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}
	preCreateContainerHookReturns struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	preCreateContainerHookReturnsOnCall map[int]struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	PreRemoveContainerHookStub        func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)
	preRemoveContainerHookMutex       sync.RWMutex
	preRemoveContainerHookArgsForCall []struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}
	preRemoveContainerHookReturns struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	preRemoveContainerHookReturnsOnCall map[int]struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	PreRemovePodSandboxHookStub        func(context.Context, *v1alpha1.PodSandboxHookRequest, ...grpc.CallOption) (*v1alpha1.PodSandboxHookResponse, error)
	preRemovePodSandboxHookMutex       sync.RWMutex
	preRemovePodSandboxHookArgsForCall []struct {
		arg1 context.Context
		arg2 *v1alpha1.PodSandboxHookRequest
		arg3 []grpc.CallOption
	}
	preRemovePodSandboxHookReturns struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}
	preRemovePodSandboxHookReturnsOnCall map[int]struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}
	PreRunPodSandboxHookStub        func(context.Context, *v1alpha1.PodSandboxHookRequest, ...grpc.CallOption) (*v1alpha1.PodSandboxHookResponse, error)
	preRunPodSandboxHookMutex       sync.RWMutex
	preRunPodSandboxHookArgsForCall []struct {
		arg1 context.Context
		arg2 *v1alpha1.PodSandboxHookRequest
		arg3 []grpc.CallOption
	}
	preRunPodSandboxHookReturns struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}
	preRunPodSandboxHookReturnsOnCall map[int]struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}
	PreStartContainerHookStub        func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)
	preStartContainerHookMutex       sync.RWMutex
	preStartContainerHookArgsForCall []struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}
	preStartContainerHookReturns struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	preStartContainerHookReturnsOnCall map[int]struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	PreUpdateContainerResourcesHookStub        func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)
	preUpdateContainerResourcesHookMutex       sync.RWMutex
	preUpdateContainerResourcesHookArgsForCall []struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}
	preUpdateContainerResourcesHookReturns struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	preUpdateContainerResourcesHookReturnsOnCall map[int]struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeRuntimeHookServiceClient) PostStartContainerHook(arg1 context.Context, arg2 *v1alpha1.ContainerResourceHookRequest, arg3 ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error) {
	fake.postStartContainerHookMutex.Lock()
	ret, specificReturn := fake.postStartContainerHookReturnsOnCall[len(fake.postStartContainerHookArgsForCall)]
	fake.postStartContainerHookArgsForCall = append(fake.postStartContainerHookArgsForCall, struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PostStartContainerHookStub
	fakeReturns := fake.postStartContainerHookReturns
	fake.recordInvocation("PostStartContainerHook", []interface{}{arg1, arg2, arg3})
	fake.postStartContainerHookMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeHookServiceClient) PostStartContainerHookCallCount() int {
	fake.postStartContainerHookMutex.RLock()
	defer fake.postStartContainerHookMutex.RUnlock()
	return len(fake.postStartContainerHookArgsForCall)
}

func (fake *FakeRuntimeHookServiceClient) PostStartContainerHookCalls(stub func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)) {
	fake.postStartContainerHookMutex.Lock()
	defer fake.postStartContainerHookMutex.Unlock()
	fake.PostStartContainerHookStub = stub
}

func (fake *FakeRuntimeHookServiceClient) PostStartContainerHookArgsForCall(i int) (context.Context, *v1alpha1.ContainerResourceHookRequest, []grpc.CallOption) {
	fake.postStartContainerHookMutex.RLock()
	defer fake.postStartContainerHookMutex.RUnlock()
	argsForCall := fake.postStartContainerHookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeHookServiceClient) PostStartContainerHookReturns(result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.postStartContainerHookMutex.Lock()
	defer fake.postStartContainerHookMutex.Unlock()
	fake.PostStartContainerHookStub = nil
	fake.postStartContainerHookReturns = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PostStartContainerHookReturnsOnCall(i int, result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.postStartContainerHookMutex.Lock()
	defer fake.postStartContainerHookMutex.Unlock()
	fake.PostStartContainerHookStub = nil
	if fake.postStartContainerHookReturnsOnCall == nil {
		fake.postStartContainerHookReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.ContainerResourceHookResponse
			result2 error
		})
	}
	fake.postStartContainerHookReturnsOnCall[i] = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PostStopContainerHook(arg1 context.Context, arg2 *v1alpha1.ContainerResourceHookRequest, arg3 ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error) {
	fake.postStopContainerHookMutex.Lock()
	ret, specificReturn := fake.postStopContainerHookReturnsOnCall[len(fake.postStopContainerHookArgsForCall)]
	fake.postStopContainerHookArgsForCall = append(fake.postStopContainerHookArgsForCall, struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PostStopContainerHookStub
	fakeReturns := fake.postStopContainerHookReturns
	fake.recordInvocation("PostStopContainerHook", []interface{}{arg1, arg2, arg3})
	fake.postStopContainerHookMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeHookServiceClient) PostStopContainerHookCallCount() int {
	fake.postStopContainerHookMutex.RLock()
	defer fake.postStopContainerHookMutex.RUnlock()
	return len(fake.postStopContainerHookArgsForCall)
}

func (fake *FakeRuntimeHookServiceClient) PostStopContainerHookCalls(stub func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)) {
	fake.postStopContainerHookMutex.Lock()
	defer fake.postStopContainerHookMutex.Unlock()
	fake.PostStopContainerHookStub = stub
}

func (fake *FakeRuntimeHookServiceClient) PostStopContainerHookArgsForCall(i int) (context.Context, *v1alpha1.ContainerResourceHookRequest, []grpc.CallOption) {
	fake.postStopContainerHookMutex.RLock()
	defer fake.postStopContainerHookMutex.RUnlock()
	argsForCall := fake.postStopContainerHookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeHookServiceClient) PostStopContainerHookReturns(result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.postStopContainerHookMutex.Lock()
	defer fake.postStopContainerHookMutex.Unlock()
	fake.PostStopContainerHookStub = nil
	fake.postStopContainerHookReturns = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PostStopContainerHookReturnsOnCall(i int, result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.postStopContainerHookMutex.Lock()
	defer fake.postStopContainerHookMutex.Unlock()
	fake.PostStopContainerHookStub = nil
	if fake.postStopContainerHookReturnsOnCall == nil {
		fake.postStopContainerHookReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.ContainerResourceHookResponse
			result2 error
		})
	}
	fake.postStopContainerHookReturnsOnCall[i] = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PostStopPodSandboxHook(arg1 context.Context, arg2 *v1alpha1.PodSandboxHookRequest, arg3 ...grpc.CallOption) (*v1alpha1.PodSandboxHookResponse, error) {
	fake.postStopPodSandboxHookMutex.Lock()
	ret, specificReturn := fake.postStopPodSandboxHookReturnsOnCall[len(fake.postStopPodSandboxHookArgsForCall)]
	fake.postStopPodSandboxHookArgsForCall = append(fake.postStopPodSandboxHookArgsForCall, struct {
		arg1 context.Context
		arg2 *v1alpha1.PodSandboxHookRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PostStopPodSandboxHookStub
	fakeReturns := fake.postStopPodSandboxHookReturns
	fake.recordInvocation("PostStopPodSandboxHook", []interface{}{arg1, arg2, arg3})
	fake.postStopPodSandboxHookMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeHookServiceClient) PostStopPodSandboxHookCallCount() int {
	fake.postStopPodSandboxHookMutex.RLock()
	defer fake.postStopPodSandboxHookMutex.RUnlock()
	return len(fake.postStopPodSandboxHookArgsForCall)
}

func (fake *FakeRuntimeHookServiceClient) PostStopPodSandboxHookCalls(stub func(context.Context, *v1alpha1.PodSandboxHookRequest, ...grpc.CallOption) (*v1alpha1.PodSandboxHookResponse, error)) {
	fake.postStopPodSandboxHookMutex.Lock()
	defer fake.postStopPodSandboxHookMutex.Unlock()
	fake.PostStopPodSandboxHookStub = stub
}

func (fake *FakeRuntimeHookServiceClient) PostStopPodSandboxHookArgsForCall(i int) (context.Context, *v1alpha1.PodSandboxHookRequest, []grpc.CallOption) {
	fake.postStopPodSandboxHookMutex.RLock()
	defer fake.postStopPodSandboxHookMutex.RUnlock()
	argsForCall := fake.postStopPodSandboxHookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeHookServiceClient) PostStopPodSandboxHookReturns(result1 *v1alpha1.PodSandboxHookResponse, result2 error) {
	fake.postStopPodSandboxHookMutex.Lock()
	defer fake.postStopPodSandboxHookMutex.Unlock()
	fake.PostStopPodSandboxHookStub = nil
	fake.postStopPodSandboxHookReturns = struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PostStopPodSandboxHookReturnsOnCall(i int, result1 *v1alpha1.PodSandboxHookResponse, result2 error) {
	fake.postStopPodSandboxHookMutex.Lock()
	defer fake.postStopPodSandboxHookMutex.Unlock()
	fake.PostStopPodSandboxHookStub = nil
	if fake.postStopPodSandboxHookReturnsOnCall == nil {
		fake.postStopPodSandboxHookReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.PodSandboxHookResponse
			result2 error
		})
	}
	fake.postStopPodSandboxHookReturnsOnCall[i] = struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreCreateContainerHook(arg1 context.Context, arg2 *v1alpha1.ContainerResourceHookRequest, arg3 ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error) {
	fake.preCreateContainerHookMutex.Lock()
	ret, specificReturn := fake.preCreateContainerHookReturnsOnCall[len(fake.preCreateContainerHookArgsForCall)]
	fake.preCreateContainerHookArgsForCall = append(fake.preCreateContainerHookArgsForCall, struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PreCreateContainerHookStub
	fakeReturns := fake.preCreateContainerHookReturns
	fake.recordInvocation("PreCreateContainerHook", []interface{}{arg1, arg2, arg3})
	fake.preCreateContainerHookMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeHookServiceClient) PreCreateContainerHookCallCount() int {
	fake.preCreateContainerHookMutex.RLock()
	defer fake.preCreateContainerHookMutex.RUnlock()
	return len(fake.preCreateContainerHookArgsForCall)
}

func (fake *FakeRuntimeHookServiceClient) PreCreateContainerHookCalls(stub func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)) {
	fake.preCreateContainerHookMutex.Lock()
	defer fake.preCreateContainerHookMutex.Unlock()
	fake.PreCreateContainerHookStub = stub
}

func (fake *FakeRuntimeHookServiceClient) PreCreateContainerHookArgsForCall(i int) (context.Context, *v1alpha1.ContainerResourceHookRequest, []grpc.CallOption) {
	fake.preCreateContainerHookMutex.RLock()
	defer fake.preCreateContainerHookMutex.RUnlock()
	argsForCall := fake.preCreateContainerHookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeHookServiceClient) PreCreateContainerHookReturns(result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.preCreateContainerHookMutex.Lock()
	defer fake.preCreateContainerHookMutex.Unlock()
	fake.PreCreateContainerHookStub = nil
	fake.preCreateContainerHookReturns = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreCreateContainerHookReturnsOnCall(i int, result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.preCreateContainerHookMutex.Lock()
	defer fake.preCreateContainerHookMutex.Unlock()
	fake.PreCreateContainerHookStub = nil
	if fake.preCreateContainerHookReturnsOnCall == nil {
		fake.preCreateContainerHookReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.ContainerResourceHookResponse
			result2 error
		})
	}
	fake.preCreateContainerHookReturnsOnCall[i] = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreRemoveContainerHook(arg1 context.Context, arg2 *v1alpha1.ContainerResourceHookRequest, arg3 ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error) {
	fake.preRemoveContainerHookMutex.Lock()
	ret, specificReturn := fake.preRemoveContainerHookReturnsOnCall[len(fake.preRemoveContainerHookArgsForCall)]
	fake.preRemoveContainerHookArgsForCall = append(fake.preRemoveContainerHookArgsForCall, struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PreRemoveContainerHookStub
	fakeReturns := fake.preRemoveContainerHookReturns
	fake.recordInvocation("PreRemoveContainerHook", []interface{}{arg1, arg2, arg3})
	fake.preRemoveContainerHookMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeHookServiceClient) PreRemoveContainerHookCallCount() int {
	fake.preRemoveContainerHookMutex.RLock()
	defer fake.preRemoveContainerHookMutex.RUnlock()
	return len(fake.preRemoveContainerHookArgsForCall)
}

func (fake *FakeRuntimeHookServiceClient) PreRemoveContainerHookCalls(stub func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)) {
	fake.preRemoveContainerHookMutex.Lock()
	defer fake.preRemoveContainerHookMutex.Unlock()
	fake.PreRemoveContainerHookStub = stub
}

func (fake *FakeRuntimeHookServiceClient) PreRemoveContainerHookArgsForCall(i int) (context.Context, *v1alpha1.ContainerResourceHookRequest, []grpc.CallOption) {
	fake.preRemoveContainerHookMutex.RLock()
	defer fake.preRemoveContainerHookMutex.RUnlock()
	argsForCall := fake.preRemoveContainerHookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeHookServiceClient) PreRemoveContainerHookReturns(result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.preRemoveContainerHookMutex.Lock()
	defer fake.preRemoveContainerHookMutex.Unlock()
	fake.PreRemoveContainerHookStub = nil
	fake.preRemoveContainerHookReturns = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreRemoveContainerHookReturnsOnCall(i int, result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.preRemoveContainerHookMutex.Lock()
	defer fake.preRemoveContainerHookMutex.Unlock()
	fake.PreRemoveContainerHookStub = nil
	if fake.preRemoveContainerHookReturnsOnCall == nil {
		fake.preRemoveContainerHookReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.ContainerResourceHookResponse
			result2 error
		})
	}
	fake.preRemoveContainerHookReturnsOnCall[i] = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreRemovePodSandboxHook(arg1 context.Context, arg2 *v1alpha1.PodSandboxHookRequest, arg3 ...grpc.CallOption) (*v1alpha1.PodSandboxHookResponse, error) {
	fake.preRemovePodSandboxHookMutex.Lock()
	ret, specificReturn := fake.preRemovePodSandboxHookReturnsOnCall[len(fake.preRemovePodSandboxHookArgsForCall)]
	fake.preRemovePodSandboxHookArgsForCall = append(fake.preRemovePodSandboxHookArgsForCall, struct {
		arg1 context.Context
		arg2 *v1alpha1.PodSandboxHookRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PreRemovePodSandboxHookStub
	fakeReturns := fake.preRemovePodSandboxHookReturns
	fake.recordInvocation("PreRemovePodSandboxHook", []interface{}{arg1, arg2, arg3})
	fake.preRemovePodSandboxHookMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeHookServiceClient) PreRemovePodSandboxHookCallCount() int {
	fake.preRemovePodSandboxHookMutex.RLock()
	defer fake.preRemovePodSandboxHookMutex.RUnlock()
	return len(fake.preRemovePodSandboxHookArgsForCall)
}

func (fake *FakeRuntimeHookServiceClient) PreRemovePodSandboxHookCalls(stub func(context.Context, *v1alpha1.PodSandboxHookRequest, ...grpc.CallOption) (*v1alpha1.PodSandboxHookResponse, error)) {
	fake.preRemovePodSandboxHookMutex.Lock()
	defer fake.preRemovePodSandboxHookMutex.Unlock()
	fake.PreRemovePodSandboxHookStub = stub
}

func (fake *FakeRuntimeHookServiceClient) PreRemovePodSandboxHookArgsForCall(i int) (context.Context, *v1alpha1.PodSandboxHookRequest, []grpc.CallOption) {
	fake.preRemovePodSandboxHookMutex.RLock()
	defer fake.preRemovePodSandboxHookMutex.RUnlock()
	argsForCall := fake.preRemovePodSandboxHookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeHookServiceClient) PreRemovePodSandboxHookReturns(result1 *v1alpha1.PodSandboxHookResponse, result2 error) {
	fake.preRemovePodSandboxHookMutex.Lock()
	defer fake.preRemovePodSandboxHookMutex.Unlock()
	fake.PreRemovePodSandboxHookStub = nil
	fake.preRemovePodSandboxHookReturns = struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreRemovePodSandboxHookReturnsOnCall(i int, result1 *v1alpha1.PodSandboxHookResponse, result2 error) {
	fake.preRemovePodSandboxHookMutex.Lock()
	defer fake.preRemovePodSandboxHookMutex.Unlock()
	fake.PreRemovePodSandboxHookStub = nil
	if fake.preRemovePodSandboxHookReturnsOnCall == nil {
		fake.preRemovePodSandboxHookReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.PodSandboxHookResponse
			result2 error
		})
	}
	fake.preRemovePodSandboxHookReturnsOnCall[i] = struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreRunPodSandboxHook(arg1 context.Context, arg2 *v1alpha1.PodSandboxHookRequest, arg3 ...grpc.CallOption) (*v1alpha1.PodSandboxHookResponse, error) {
	fake.preRunPodSandboxHookMutex.Lock()
	ret, specificReturn := fake.preRunPodSandboxHookReturnsOnCall[len(fake.preRunPodSandboxHookArgsForCall)]
	fake.preRunPodSandboxHookArgsForCall = append(fake.preRunPodSandboxHookArgsForCall, struct {
		arg1 context.Context
		arg2 *v1alpha1.PodSandboxHookRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PreRunPodSandboxHookStub
	fakeReturns := fake.preRunPodSandboxHookReturns
	fake.recordInvocation("PreRunPodSandboxHook", []interface{}{arg1, arg2, arg3})
	fake.preRunPodSandboxHookMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeHookServiceClient) PreRunPodSandboxHookCallCount() int {
	fake.preRunPodSandboxHookMutex.RLock()
	defer fake.preRunPodSandboxHookMutex.RUnlock()
	return len(fake.preRunPodSandboxHookArgsForCall)
}

func (fake *FakeRuntimeHookServiceClient) PreRunPodSandboxHookCalls(stub func(context.Context, *v1alpha1.PodSandboxHookRequest, ...grpc.CallOption) (*v1alpha1.PodSandboxHookResponse, error)) {
	fake.preRunPodSandboxHookMutex.Lock()
	defer fake.preRunPodSandboxHookMutex.Unlock()
	fake.PreRunPodSandboxHookStub = stub
}

func (fake *FakeRuntimeHookServiceClient) PreRunPodSandboxHookArgsForCall(i int) (context.Context, *v1alpha1.PodSandboxHookRequest, []grpc.CallOption) {
	fake.preRunPodSandboxHookMutex.RLock()
	defer fake.preRunPodSandboxHookMutex.RUnlock()
	argsForCall := fake.preRunPodSandboxHookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeHookServiceClient) PreRunPodSandboxHookReturns(result1 *v1alpha1.PodSandboxHookResponse, result2 error) {
	fake.preRunPodSandboxHookMutex.Lock()
	defer fake.preRunPodSandboxHookMutex.Unlock()
	fake.PreRunPodSandboxHookStub = nil
	fake.preRunPodSandboxHookReturns = struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreRunPodSandboxHookReturnsOnCall(i int, result1 *v1alpha1.PodSandboxHookResponse, result2 error) {
	fake.preRunPodSandboxHookMutex.Lock()
	defer fake.preRunPodSandboxHookMutex.Unlock()
	fake.PreRunPodSandboxHookStub = nil
	if fake.preRunPodSandboxHookReturnsOnCall == nil {
		fake.preRunPodSandboxHookReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.PodSandboxHookResponse
			result2 error
		})
	}
	fake.preRunPodSandboxHookReturnsOnCall[i] = struct {
		result1 *v1alpha1.PodSandboxHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreStartContainerHook(arg1 context.Context, arg2 *v1alpha1.ContainerResourceHookRequest, arg3 ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error) {
	fake.preStartContainerHookMutex.Lock()
	ret, specificReturn := fake.preStartContainerHookReturnsOnCall[len(fake.preStartContainerHookArgsForCall)]
	fake.preStartContainerHookArgsForCall = append(fake.preStartContainerHookArgsForCall, struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PreStartContainerHookStub
	fakeReturns := fake.preStartContainerHookReturns
	fake.recordInvocation("PreStartContainerHook", []interface{}{arg1, arg2, arg3})
	fake.preStartContainerHookMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeHookServiceClient) PreStartContainerHookCallCount() int {
	fake.preStartContainerHookMutex.RLock()
	defer fake.preStartContainerHookMutex.RUnlock()
	return len(fake.preStartContainerHookArgsForCall)
}

func (fake *FakeRuntimeHookServiceClient) PreStartContainerHookCalls(stub func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)) {
	fake.preStartContainerHookMutex.Lock()
	defer fake.preStartContainerHookMutex.Unlock()
	fake.PreStartContainerHookStub = stub
}

func (fake *FakeRuntimeHookServiceClient) PreStartContainerHookArgsForCall(i int) (context.Context, *v1alpha1.ContainerResourceHookRequest, []grpc.CallOption) {
	fake.preStartContainerHookMutex.RLock()
	defer fake.preStartContainerHookMutex.RUnlock()
	argsForCall := fake.preStartContainerHookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeHookServiceClient) PreStartContainerHookReturns(result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.preStartContainerHookMutex.Lock()
	defer fake.preStartContainerHookMutex.Unlock()
	fake.PreStartContainerHookStub = nil
	fake.preStartContainerHookReturns = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreStartContainerHookReturnsOnCall(i int, result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.preStartContainerHookMutex.Lock()
	defer fake.preStartContainerHookMutex.Unlock()
	fake.PreStartContainerHookStub = nil
	if fake.preStartContainerHookReturnsOnCall == nil {
		fake.preStartContainerHookReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.ContainerResourceHookResponse
			result2 error
		})
	}
	fake.preStartContainerHookReturnsOnCall[i] = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreUpdateContainerResourcesHook(arg1 context.Context, arg2 *v1alpha1.ContainerResourceHookRequest, arg3 ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error) {
	fake.preUpdateContainerResourcesHookMutex.Lock()
	ret, specificReturn := fake.preUpdateContainerResourcesHookReturnsOnCall[len(fake.preUpdateContainerResourcesHookArgsForCall)]
	fake.preUpdateContainerResourcesHookArgsForCall = append(fake.preUpdateContainerResourcesHookArgsForCall, struct {
		arg1 context.Context
		arg2 *v1alpha1.ContainerResourceHookRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PreUpdateContainerResourcesHookStub
	fakeReturns := fake.preUpdateContainerResourcesHookReturns
	fake.recordInvocation("PreUpdateContainerResourcesHook", []interface{}{arg1, arg2, arg3})
	fake.preUpdateContainerResourcesHookMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeHookServiceClient) PreUpdateContainerResourcesHookCallCount() int {
	fake.preUpdateContainerResourcesHookMutex.RLock()
	defer fake.preUpdateContainerResourcesHookMutex.RUnlock()
	return len(fake.preUpdateContainerResourcesHookArgsForCall)
}

func (fake *FakeRuntimeHookServiceClient) PreUpdateContainerResourcesHookCalls(stub func(context.Context, *v1alpha1.ContainerResourceHookRequest, ...grpc.CallOption) (*v1alpha1.ContainerResourceHookResponse, error)) {
	fake.preUpdateContainerResourcesHookMutex.Lock()
	defer fake.preUpdateContainerResourcesHookMutex.Unlock()
	fake.PreUpdateContainerResourcesHookStub = stub
}

func (fake *FakeRuntimeHookServiceClient) PreUpdateContainerResourcesHookArgsForCall(i int) (context.Context, *v1alpha1.ContainerResourceHookRequest, []grpc.CallOption) {
	fake.preUpdateContainerResourcesHookMutex.RLock()
	defer fake.preUpdateContainerResourcesHookMutex.RUnlock()
	argsForCall := fake.preUpdateContainerResourcesHookArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeHookServiceClient) PreUpdateContainerResourcesHookReturns(result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.preUpdateContainerResourcesHookMutex.Lock()
	defer fake.preUpdateContainerResourcesHookMutex.Unlock()
	fake.PreUpdateContainerResourcesHookStub = nil
	fake.preUpdateContainerResourcesHookReturns = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) PreUpdateContainerResourcesHookReturnsOnCall(i int, result1 *v1alpha1.ContainerResourceHookResponse, result2 error) {
	fake.preUpdateContainerResourcesHookMutex.Lock()
	defer fake.preUpdateContainerResourcesHookMutex.Unlock()
	fake.PreUpdateContainerResourcesHookStub = nil
	if fake.preUpdateContainerResourcesHookReturnsOnCall == nil {
		fake.preUpdateContainerResourcesHookReturnsOnCall = make(map[int]struct {
			result1 *v1alpha1.ContainerResourceHookResponse
			result2 error
		})
	}
	fake.preUpdateContainerResourcesHookReturnsOnCall[i] = struct {
		result1 *v1alpha1.ContainerResourceHookResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeHookServiceClient) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.postStartContainerHookMutex.RLock()
	defer fake.postStartContainerHookMutex.RUnlock()
	fake.postStopContainerHookMutex.RLock()
	defer fake.postStopContainerHookMutex.RUnlock()
	fake.postStopPodSandboxHookMutex.RLock()
	defer fake.postStopPodSandboxHookMutex.RUnlock()
	fake.preCreateContainerHookMutex.RLock()
	defer fake.preCreateContainerHookMutex.RUnlock()
	fake.preRemoveContainerHookMutex.RLock()
	defer fake.preRemoveContainerHookMutex.RUnlock()
	fake.preRemovePodSandboxHookMutex.RLock()
	defer fake.preRemovePodSandboxHookMutex.RUnlock()
	fake.preRunPodSandboxHookMutex.RLock()
	defer fake.preRunPodSandboxHookMutex.RUnlock()
	fake.preStartContainerHookMutex.RLock()
	defer fake.preStartContainerHookMutex.RUnlock()
	fake.preUpdateContainerResourcesHookMutex.RLock()
	defer fake.preUpdateContainerResourcesHookMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeRuntimeHookServiceClient) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ v1alpha1.RuntimeHookServiceClient = new(FakeRuntimeHookServiceClient)
