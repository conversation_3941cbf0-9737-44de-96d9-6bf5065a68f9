// Code generated by counterfeiter. DO NOT EDIT.
package fake

import (
	"net/http"
	"sync"

	"kunpeng.huawei.com/kunpeng-cloud-computing/pkg/kunpeng-tap/server/docker"
)

type FakeDockerHandler struct {
	DirectStub        func(http.ResponseWriter, *http.Request) string
	directMutex       sync.RWMutex
	directArgsForCall []struct {
		arg1 http.ResponseWriter
		arg2 *http.Request
	}
	directReturns struct {
		result1 string
	}
	directReturnsOnCall map[int]struct {
		result1 string
	}
	HandleCreateContainerStub        func() func(http.ResponseWriter, *http.Request)
	handleCreateContainerMutex       sync.RWMutex
	handleCreateContainerArgsForCall []struct {
	}
	handleCreateContainerReturns struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	handleCreateContainerReturnsOnCall map[int]struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	HandleDeleteContainerStub        func() func(http.ResponseWriter, *http.Request)
	handleDeleteContainerMutex       sync.RWMutex
	handleDeleteContainerArgsForCall []struct {
	}
	handleDeleteContainerReturns struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	handleDeleteContainerReturnsOnCall map[int]struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	HandleStartContainerStub        func() func(http.ResponseWriter, *http.Request)
	handleStartContainerMutex       sync.RWMutex
	handleStartContainerArgsForCall []struct {
	}
	handleStartContainerReturns struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	handleStartContainerReturnsOnCall map[int]struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	HandleStopContainerStub        func() func(http.ResponseWriter, *http.Request)
	handleStopContainerMutex       sync.RWMutex
	handleStopContainerArgsForCall []struct {
	}
	handleStopContainerReturns struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	handleStopContainerReturnsOnCall map[int]struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	HandleUpdateContainerStub        func() func(http.ResponseWriter, *http.Request)
	handleUpdateContainerMutex       sync.RWMutex
	handleUpdateContainerArgsForCall []struct {
	}
	handleUpdateContainerReturns struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	handleUpdateContainerReturnsOnCall map[int]struct {
		result1 func(http.ResponseWriter, *http.Request)
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeDockerHandler) Direct(arg1 http.ResponseWriter, arg2 *http.Request) string {
	fake.directMutex.Lock()
	ret, specificReturn := fake.directReturnsOnCall[len(fake.directArgsForCall)]
	fake.directArgsForCall = append(fake.directArgsForCall, struct {
		arg1 http.ResponseWriter
		arg2 *http.Request
	}{arg1, arg2})
	stub := fake.DirectStub
	fakeReturns := fake.directReturns
	fake.recordInvocation("Direct", []interface{}{arg1, arg2})
	fake.directMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2)
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeDockerHandler) DirectCallCount() int {
	fake.directMutex.RLock()
	defer fake.directMutex.RUnlock()
	return len(fake.directArgsForCall)
}

func (fake *FakeDockerHandler) DirectCalls(stub func(http.ResponseWriter, *http.Request) string) {
	fake.directMutex.Lock()
	defer fake.directMutex.Unlock()
	fake.DirectStub = stub
}

func (fake *FakeDockerHandler) DirectArgsForCall(i int) (http.ResponseWriter, *http.Request) {
	fake.directMutex.RLock()
	defer fake.directMutex.RUnlock()
	argsForCall := fake.directArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2
}

func (fake *FakeDockerHandler) DirectReturns(result1 string) {
	fake.directMutex.Lock()
	defer fake.directMutex.Unlock()
	fake.DirectStub = nil
	fake.directReturns = struct {
		result1 string
	}{result1}
}

func (fake *FakeDockerHandler) DirectReturnsOnCall(i int, result1 string) {
	fake.directMutex.Lock()
	defer fake.directMutex.Unlock()
	fake.DirectStub = nil
	if fake.directReturnsOnCall == nil {
		fake.directReturnsOnCall = make(map[int]struct {
			result1 string
		})
	}
	fake.directReturnsOnCall[i] = struct {
		result1 string
	}{result1}
}

func (fake *FakeDockerHandler) HandleCreateContainer() func(http.ResponseWriter, *http.Request) {
	fake.handleCreateContainerMutex.Lock()
	ret, specificReturn := fake.handleCreateContainerReturnsOnCall[len(fake.handleCreateContainerArgsForCall)]
	fake.handleCreateContainerArgsForCall = append(fake.handleCreateContainerArgsForCall, struct {
	}{})
	stub := fake.HandleCreateContainerStub
	fakeReturns := fake.handleCreateContainerReturns
	fake.recordInvocation("HandleCreateContainer", []interface{}{})
	fake.handleCreateContainerMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeDockerHandler) HandleCreateContainerCallCount() int {
	fake.handleCreateContainerMutex.RLock()
	defer fake.handleCreateContainerMutex.RUnlock()
	return len(fake.handleCreateContainerArgsForCall)
}

func (fake *FakeDockerHandler) HandleCreateContainerCalls(stub func() func(http.ResponseWriter, *http.Request)) {
	fake.handleCreateContainerMutex.Lock()
	defer fake.handleCreateContainerMutex.Unlock()
	fake.HandleCreateContainerStub = stub
}

func (fake *FakeDockerHandler) HandleCreateContainerReturns(result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleCreateContainerMutex.Lock()
	defer fake.handleCreateContainerMutex.Unlock()
	fake.HandleCreateContainerStub = nil
	fake.handleCreateContainerReturns = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) HandleCreateContainerReturnsOnCall(i int, result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleCreateContainerMutex.Lock()
	defer fake.handleCreateContainerMutex.Unlock()
	fake.HandleCreateContainerStub = nil
	if fake.handleCreateContainerReturnsOnCall == nil {
		fake.handleCreateContainerReturnsOnCall = make(map[int]struct {
			result1 func(http.ResponseWriter, *http.Request)
		})
	}
	fake.handleCreateContainerReturnsOnCall[i] = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) HandleDeleteContainer() func(http.ResponseWriter, *http.Request) {
	fake.handleDeleteContainerMutex.Lock()
	ret, specificReturn := fake.handleDeleteContainerReturnsOnCall[len(fake.handleDeleteContainerArgsForCall)]
	fake.handleDeleteContainerArgsForCall = append(fake.handleDeleteContainerArgsForCall, struct {
	}{})
	stub := fake.HandleDeleteContainerStub
	fakeReturns := fake.handleDeleteContainerReturns
	fake.recordInvocation("HandleDeleteContainer", []interface{}{})
	fake.handleDeleteContainerMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeDockerHandler) HandleDeleteContainerCallCount() int {
	fake.handleDeleteContainerMutex.RLock()
	defer fake.handleDeleteContainerMutex.RUnlock()
	return len(fake.handleDeleteContainerArgsForCall)
}

func (fake *FakeDockerHandler) HandleDeleteContainerCalls(stub func() func(http.ResponseWriter, *http.Request)) {
	fake.handleDeleteContainerMutex.Lock()
	defer fake.handleDeleteContainerMutex.Unlock()
	fake.HandleDeleteContainerStub = stub
}

func (fake *FakeDockerHandler) HandleDeleteContainerReturns(result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleDeleteContainerMutex.Lock()
	defer fake.handleDeleteContainerMutex.Unlock()
	fake.HandleDeleteContainerStub = nil
	fake.handleDeleteContainerReturns = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) HandleDeleteContainerReturnsOnCall(i int, result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleDeleteContainerMutex.Lock()
	defer fake.handleDeleteContainerMutex.Unlock()
	fake.HandleDeleteContainerStub = nil
	if fake.handleDeleteContainerReturnsOnCall == nil {
		fake.handleDeleteContainerReturnsOnCall = make(map[int]struct {
			result1 func(http.ResponseWriter, *http.Request)
		})
	}
	fake.handleDeleteContainerReturnsOnCall[i] = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) HandleStartContainer() func(http.ResponseWriter, *http.Request) {
	fake.handleStartContainerMutex.Lock()
	ret, specificReturn := fake.handleStartContainerReturnsOnCall[len(fake.handleStartContainerArgsForCall)]
	fake.handleStartContainerArgsForCall = append(fake.handleStartContainerArgsForCall, struct {
	}{})
	stub := fake.HandleStartContainerStub
	fakeReturns := fake.handleStartContainerReturns
	fake.recordInvocation("HandleStartContainer", []interface{}{})
	fake.handleStartContainerMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeDockerHandler) HandleStartContainerCallCount() int {
	fake.handleStartContainerMutex.RLock()
	defer fake.handleStartContainerMutex.RUnlock()
	return len(fake.handleStartContainerArgsForCall)
}

func (fake *FakeDockerHandler) HandleStartContainerCalls(stub func() func(http.ResponseWriter, *http.Request)) {
	fake.handleStartContainerMutex.Lock()
	defer fake.handleStartContainerMutex.Unlock()
	fake.HandleStartContainerStub = stub
}

func (fake *FakeDockerHandler) HandleStartContainerReturns(result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleStartContainerMutex.Lock()
	defer fake.handleStartContainerMutex.Unlock()
	fake.HandleStartContainerStub = nil
	fake.handleStartContainerReturns = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) HandleStartContainerReturnsOnCall(i int, result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleStartContainerMutex.Lock()
	defer fake.handleStartContainerMutex.Unlock()
	fake.HandleStartContainerStub = nil
	if fake.handleStartContainerReturnsOnCall == nil {
		fake.handleStartContainerReturnsOnCall = make(map[int]struct {
			result1 func(http.ResponseWriter, *http.Request)
		})
	}
	fake.handleStartContainerReturnsOnCall[i] = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) HandleStopContainer() func(http.ResponseWriter, *http.Request) {
	fake.handleStopContainerMutex.Lock()
	ret, specificReturn := fake.handleStopContainerReturnsOnCall[len(fake.handleStopContainerArgsForCall)]
	fake.handleStopContainerArgsForCall = append(fake.handleStopContainerArgsForCall, struct {
	}{})
	stub := fake.HandleStopContainerStub
	fakeReturns := fake.handleStopContainerReturns
	fake.recordInvocation("HandleStopContainer", []interface{}{})
	fake.handleStopContainerMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeDockerHandler) HandleStopContainerCallCount() int {
	fake.handleStopContainerMutex.RLock()
	defer fake.handleStopContainerMutex.RUnlock()
	return len(fake.handleStopContainerArgsForCall)
}

func (fake *FakeDockerHandler) HandleStopContainerCalls(stub func() func(http.ResponseWriter, *http.Request)) {
	fake.handleStopContainerMutex.Lock()
	defer fake.handleStopContainerMutex.Unlock()
	fake.HandleStopContainerStub = stub
}

func (fake *FakeDockerHandler) HandleStopContainerReturns(result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleStopContainerMutex.Lock()
	defer fake.handleStopContainerMutex.Unlock()
	fake.HandleStopContainerStub = nil
	fake.handleStopContainerReturns = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) HandleStopContainerReturnsOnCall(i int, result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleStopContainerMutex.Lock()
	defer fake.handleStopContainerMutex.Unlock()
	fake.HandleStopContainerStub = nil
	if fake.handleStopContainerReturnsOnCall == nil {
		fake.handleStopContainerReturnsOnCall = make(map[int]struct {
			result1 func(http.ResponseWriter, *http.Request)
		})
	}
	fake.handleStopContainerReturnsOnCall[i] = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) HandleUpdateContainer() func(http.ResponseWriter, *http.Request) {
	fake.handleUpdateContainerMutex.Lock()
	ret, specificReturn := fake.handleUpdateContainerReturnsOnCall[len(fake.handleUpdateContainerArgsForCall)]
	fake.handleUpdateContainerArgsForCall = append(fake.handleUpdateContainerArgsForCall, struct {
	}{})
	stub := fake.HandleUpdateContainerStub
	fakeReturns := fake.handleUpdateContainerReturns
	fake.recordInvocation("HandleUpdateContainer", []interface{}{})
	fake.handleUpdateContainerMutex.Unlock()
	if stub != nil {
		return stub()
	}
	if specificReturn {
		return ret.result1
	}
	return fakeReturns.result1
}

func (fake *FakeDockerHandler) HandleUpdateContainerCallCount() int {
	fake.handleUpdateContainerMutex.RLock()
	defer fake.handleUpdateContainerMutex.RUnlock()
	return len(fake.handleUpdateContainerArgsForCall)
}

func (fake *FakeDockerHandler) HandleUpdateContainerCalls(stub func() func(http.ResponseWriter, *http.Request)) {
	fake.handleUpdateContainerMutex.Lock()
	defer fake.handleUpdateContainerMutex.Unlock()
	fake.HandleUpdateContainerStub = stub
}

func (fake *FakeDockerHandler) HandleUpdateContainerReturns(result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleUpdateContainerMutex.Lock()
	defer fake.handleUpdateContainerMutex.Unlock()
	fake.HandleUpdateContainerStub = nil
	fake.handleUpdateContainerReturns = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) HandleUpdateContainerReturnsOnCall(i int, result1 func(http.ResponseWriter, *http.Request)) {
	fake.handleUpdateContainerMutex.Lock()
	defer fake.handleUpdateContainerMutex.Unlock()
	fake.HandleUpdateContainerStub = nil
	if fake.handleUpdateContainerReturnsOnCall == nil {
		fake.handleUpdateContainerReturnsOnCall = make(map[int]struct {
			result1 func(http.ResponseWriter, *http.Request)
		})
	}
	fake.handleUpdateContainerReturnsOnCall[i] = struct {
		result1 func(http.ResponseWriter, *http.Request)
	}{result1}
}

func (fake *FakeDockerHandler) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.directMutex.RLock()
	defer fake.directMutex.RUnlock()
	fake.handleCreateContainerMutex.RLock()
	defer fake.handleCreateContainerMutex.RUnlock()
	fake.handleDeleteContainerMutex.RLock()
	defer fake.handleDeleteContainerMutex.RUnlock()
	fake.handleStartContainerMutex.RLock()
	defer fake.handleStartContainerMutex.RUnlock()
	fake.handleStopContainerMutex.RLock()
	defer fake.handleStopContainerMutex.RUnlock()
	fake.handleUpdateContainerMutex.RLock()
	defer fake.handleUpdateContainerMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeDockerHandler) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ docker.DockerHandler = new(FakeDockerHandler)
