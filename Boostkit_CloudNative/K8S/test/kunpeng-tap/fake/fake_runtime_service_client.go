// Code generated by counterfeiter. DO NOT EDIT.
package fake

import (
	"context"
	"sync"

	"google.golang.org/grpc"
	v1 "k8s.io/cri-api/pkg/apis/runtime/v1"
)

type FakeRuntimeServiceClient struct {
	AttachStub        func(context.Context, *v1.AttachRequest, ...grpc.CallOption) (*v1.AttachResponse, error)
	attachMutex       sync.RWMutex
	attachArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.AttachRequest
		arg3 []grpc.CallOption
	}
	attachReturns struct {
		result1 *v1.AttachResponse
		result2 error
	}
	attachReturnsOnCall map[int]struct {
		result1 *v1.AttachResponse
		result2 error
	}
	CheckpointContainerStub        func(context.Context, *v1.CheckpointContainerRequest, ...grpc.CallOption) (*v1.CheckpointContainerResponse, error)
	checkpointContainerMutex       sync.RWMutex
	checkpointContainerArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.CheckpointContainerRequest
		arg3 []grpc.CallOption
	}
	checkpointContainerReturns struct {
		result1 *v1.CheckpointContainerResponse
		result2 error
	}
	checkpointContainerReturnsOnCall map[int]struct {
		result1 *v1.CheckpointContainerResponse
		result2 error
	}
	ContainerStatsStub        func(context.Context, *v1.ContainerStatsRequest, ...grpc.CallOption) (*v1.ContainerStatsResponse, error)
	containerStatsMutex       sync.RWMutex
	containerStatsArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ContainerStatsRequest
		arg3 []grpc.CallOption
	}
	containerStatsReturns struct {
		result1 *v1.ContainerStatsResponse
		result2 error
	}
	containerStatsReturnsOnCall map[int]struct {
		result1 *v1.ContainerStatsResponse
		result2 error
	}
	ContainerStatusStub        func(context.Context, *v1.ContainerStatusRequest, ...grpc.CallOption) (*v1.ContainerStatusResponse, error)
	containerStatusMutex       sync.RWMutex
	containerStatusArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ContainerStatusRequest
		arg3 []grpc.CallOption
	}
	containerStatusReturns struct {
		result1 *v1.ContainerStatusResponse
		result2 error
	}
	containerStatusReturnsOnCall map[int]struct {
		result1 *v1.ContainerStatusResponse
		result2 error
	}
	CreateContainerStub        func(context.Context, *v1.CreateContainerRequest, ...grpc.CallOption) (*v1.CreateContainerResponse, error)
	createContainerMutex       sync.RWMutex
	createContainerArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.CreateContainerRequest
		arg3 []grpc.CallOption
	}
	createContainerReturns struct {
		result1 *v1.CreateContainerResponse
		result2 error
	}
	createContainerReturnsOnCall map[int]struct {
		result1 *v1.CreateContainerResponse
		result2 error
	}
	ExecStub        func(context.Context, *v1.ExecRequest, ...grpc.CallOption) (*v1.ExecResponse, error)
	execMutex       sync.RWMutex
	execArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ExecRequest
		arg3 []grpc.CallOption
	}
	execReturns struct {
		result1 *v1.ExecResponse
		result2 error
	}
	execReturnsOnCall map[int]struct {
		result1 *v1.ExecResponse
		result2 error
	}
	ExecSyncStub        func(context.Context, *v1.ExecSyncRequest, ...grpc.CallOption) (*v1.ExecSyncResponse, error)
	execSyncMutex       sync.RWMutex
	execSyncArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ExecSyncRequest
		arg3 []grpc.CallOption
	}
	execSyncReturns struct {
		result1 *v1.ExecSyncResponse
		result2 error
	}
	execSyncReturnsOnCall map[int]struct {
		result1 *v1.ExecSyncResponse
		result2 error
	}
	GetContainerEventsStub        func(context.Context, *v1.GetEventsRequest, ...grpc.CallOption) (v1.RuntimeService_GetContainerEventsClient, error)
	getContainerEventsMutex       sync.RWMutex
	getContainerEventsArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.GetEventsRequest
		arg3 []grpc.CallOption
	}
	getContainerEventsReturns struct {
		result1 v1.RuntimeService_GetContainerEventsClient
		result2 error
	}
	getContainerEventsReturnsOnCall map[int]struct {
		result1 v1.RuntimeService_GetContainerEventsClient
		result2 error
	}
	ListContainerStatsStub        func(context.Context, *v1.ListContainerStatsRequest, ...grpc.CallOption) (*v1.ListContainerStatsResponse, error)
	listContainerStatsMutex       sync.RWMutex
	listContainerStatsArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ListContainerStatsRequest
		arg3 []grpc.CallOption
	}
	listContainerStatsReturns struct {
		result1 *v1.ListContainerStatsResponse
		result2 error
	}
	listContainerStatsReturnsOnCall map[int]struct {
		result1 *v1.ListContainerStatsResponse
		result2 error
	}
	ListContainersStub        func(context.Context, *v1.ListContainersRequest, ...grpc.CallOption) (*v1.ListContainersResponse, error)
	listContainersMutex       sync.RWMutex
	listContainersArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ListContainersRequest
		arg3 []grpc.CallOption
	}
	listContainersReturns struct {
		result1 *v1.ListContainersResponse
		result2 error
	}
	listContainersReturnsOnCall map[int]struct {
		result1 *v1.ListContainersResponse
		result2 error
	}
	ListMetricDescriptorsStub        func(context.Context, *v1.ListMetricDescriptorsRequest, ...grpc.CallOption) (*v1.ListMetricDescriptorsResponse, error)
	listMetricDescriptorsMutex       sync.RWMutex
	listMetricDescriptorsArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ListMetricDescriptorsRequest
		arg3 []grpc.CallOption
	}
	listMetricDescriptorsReturns struct {
		result1 *v1.ListMetricDescriptorsResponse
		result2 error
	}
	listMetricDescriptorsReturnsOnCall map[int]struct {
		result1 *v1.ListMetricDescriptorsResponse
		result2 error
	}
	ListPodSandboxStub        func(context.Context, *v1.ListPodSandboxRequest, ...grpc.CallOption) (*v1.ListPodSandboxResponse, error)
	listPodSandboxMutex       sync.RWMutex
	listPodSandboxArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ListPodSandboxRequest
		arg3 []grpc.CallOption
	}
	listPodSandboxReturns struct {
		result1 *v1.ListPodSandboxResponse
		result2 error
	}
	listPodSandboxReturnsOnCall map[int]struct {
		result1 *v1.ListPodSandboxResponse
		result2 error
	}
	ListPodSandboxMetricsStub        func(context.Context, *v1.ListPodSandboxMetricsRequest, ...grpc.CallOption) (*v1.ListPodSandboxMetricsResponse, error)
	listPodSandboxMetricsMutex       sync.RWMutex
	listPodSandboxMetricsArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ListPodSandboxMetricsRequest
		arg3 []grpc.CallOption
	}
	listPodSandboxMetricsReturns struct {
		result1 *v1.ListPodSandboxMetricsResponse
		result2 error
	}
	listPodSandboxMetricsReturnsOnCall map[int]struct {
		result1 *v1.ListPodSandboxMetricsResponse
		result2 error
	}
	ListPodSandboxStatsStub        func(context.Context, *v1.ListPodSandboxStatsRequest, ...grpc.CallOption) (*v1.ListPodSandboxStatsResponse, error)
	listPodSandboxStatsMutex       sync.RWMutex
	listPodSandboxStatsArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ListPodSandboxStatsRequest
		arg3 []grpc.CallOption
	}
	listPodSandboxStatsReturns struct {
		result1 *v1.ListPodSandboxStatsResponse
		result2 error
	}
	listPodSandboxStatsReturnsOnCall map[int]struct {
		result1 *v1.ListPodSandboxStatsResponse
		result2 error
	}
	PodSandboxStatsStub        func(context.Context, *v1.PodSandboxStatsRequest, ...grpc.CallOption) (*v1.PodSandboxStatsResponse, error)
	podSandboxStatsMutex       sync.RWMutex
	podSandboxStatsArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.PodSandboxStatsRequest
		arg3 []grpc.CallOption
	}
	podSandboxStatsReturns struct {
		result1 *v1.PodSandboxStatsResponse
		result2 error
	}
	podSandboxStatsReturnsOnCall map[int]struct {
		result1 *v1.PodSandboxStatsResponse
		result2 error
	}
	PodSandboxStatusStub        func(context.Context, *v1.PodSandboxStatusRequest, ...grpc.CallOption) (*v1.PodSandboxStatusResponse, error)
	podSandboxStatusMutex       sync.RWMutex
	podSandboxStatusArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.PodSandboxStatusRequest
		arg3 []grpc.CallOption
	}
	podSandboxStatusReturns struct {
		result1 *v1.PodSandboxStatusResponse
		result2 error
	}
	podSandboxStatusReturnsOnCall map[int]struct {
		result1 *v1.PodSandboxStatusResponse
		result2 error
	}
	PortForwardStub        func(context.Context, *v1.PortForwardRequest, ...grpc.CallOption) (*v1.PortForwardResponse, error)
	portForwardMutex       sync.RWMutex
	portForwardArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.PortForwardRequest
		arg3 []grpc.CallOption
	}
	portForwardReturns struct {
		result1 *v1.PortForwardResponse
		result2 error
	}
	portForwardReturnsOnCall map[int]struct {
		result1 *v1.PortForwardResponse
		result2 error
	}
	RemoveContainerStub        func(context.Context, *v1.RemoveContainerRequest, ...grpc.CallOption) (*v1.RemoveContainerResponse, error)
	removeContainerMutex       sync.RWMutex
	removeContainerArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.RemoveContainerRequest
		arg3 []grpc.CallOption
	}
	removeContainerReturns struct {
		result1 *v1.RemoveContainerResponse
		result2 error
	}
	removeContainerReturnsOnCall map[int]struct {
		result1 *v1.RemoveContainerResponse
		result2 error
	}
	RemovePodSandboxStub        func(context.Context, *v1.RemovePodSandboxRequest, ...grpc.CallOption) (*v1.RemovePodSandboxResponse, error)
	removePodSandboxMutex       sync.RWMutex
	removePodSandboxArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.RemovePodSandboxRequest
		arg3 []grpc.CallOption
	}
	removePodSandboxReturns struct {
		result1 *v1.RemovePodSandboxResponse
		result2 error
	}
	removePodSandboxReturnsOnCall map[int]struct {
		result1 *v1.RemovePodSandboxResponse
		result2 error
	}
	ReopenContainerLogStub        func(context.Context, *v1.ReopenContainerLogRequest, ...grpc.CallOption) (*v1.ReopenContainerLogResponse, error)
	reopenContainerLogMutex       sync.RWMutex
	reopenContainerLogArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.ReopenContainerLogRequest
		arg3 []grpc.CallOption
	}
	reopenContainerLogReturns struct {
		result1 *v1.ReopenContainerLogResponse
		result2 error
	}
	reopenContainerLogReturnsOnCall map[int]struct {
		result1 *v1.ReopenContainerLogResponse
		result2 error
	}
	RunPodSandboxStub        func(context.Context, *v1.RunPodSandboxRequest, ...grpc.CallOption) (*v1.RunPodSandboxResponse, error)
	runPodSandboxMutex       sync.RWMutex
	runPodSandboxArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.RunPodSandboxRequest
		arg3 []grpc.CallOption
	}
	runPodSandboxReturns struct {
		result1 *v1.RunPodSandboxResponse
		result2 error
	}
	runPodSandboxReturnsOnCall map[int]struct {
		result1 *v1.RunPodSandboxResponse
		result2 error
	}
	RuntimeConfigStub        func(context.Context, *v1.RuntimeConfigRequest, ...grpc.CallOption) (*v1.RuntimeConfigResponse, error)
	runtimeConfigMutex       sync.RWMutex
	runtimeConfigArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.RuntimeConfigRequest
		arg3 []grpc.CallOption
	}
	runtimeConfigReturns struct {
		result1 *v1.RuntimeConfigResponse
		result2 error
	}
	runtimeConfigReturnsOnCall map[int]struct {
		result1 *v1.RuntimeConfigResponse
		result2 error
	}
	StartContainerStub        func(context.Context, *v1.StartContainerRequest, ...grpc.CallOption) (*v1.StartContainerResponse, error)
	startContainerMutex       sync.RWMutex
	startContainerArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.StartContainerRequest
		arg3 []grpc.CallOption
	}
	startContainerReturns struct {
		result1 *v1.StartContainerResponse
		result2 error
	}
	startContainerReturnsOnCall map[int]struct {
		result1 *v1.StartContainerResponse
		result2 error
	}
	StatusStub        func(context.Context, *v1.StatusRequest, ...grpc.CallOption) (*v1.StatusResponse, error)
	statusMutex       sync.RWMutex
	statusArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.StatusRequest
		arg3 []grpc.CallOption
	}
	statusReturns struct {
		result1 *v1.StatusResponse
		result2 error
	}
	statusReturnsOnCall map[int]struct {
		result1 *v1.StatusResponse
		result2 error
	}
	StopContainerStub        func(context.Context, *v1.StopContainerRequest, ...grpc.CallOption) (*v1.StopContainerResponse, error)
	stopContainerMutex       sync.RWMutex
	stopContainerArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.StopContainerRequest
		arg3 []grpc.CallOption
	}
	stopContainerReturns struct {
		result1 *v1.StopContainerResponse
		result2 error
	}
	stopContainerReturnsOnCall map[int]struct {
		result1 *v1.StopContainerResponse
		result2 error
	}
	StopPodSandboxStub        func(context.Context, *v1.StopPodSandboxRequest, ...grpc.CallOption) (*v1.StopPodSandboxResponse, error)
	stopPodSandboxMutex       sync.RWMutex
	stopPodSandboxArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.StopPodSandboxRequest
		arg3 []grpc.CallOption
	}
	stopPodSandboxReturns struct {
		result1 *v1.StopPodSandboxResponse
		result2 error
	}
	stopPodSandboxReturnsOnCall map[int]struct {
		result1 *v1.StopPodSandboxResponse
		result2 error
	}
	UpdateContainerResourcesStub        func(context.Context, *v1.UpdateContainerResourcesRequest, ...grpc.CallOption) (*v1.UpdateContainerResourcesResponse, error)
	updateContainerResourcesMutex       sync.RWMutex
	updateContainerResourcesArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.UpdateContainerResourcesRequest
		arg3 []grpc.CallOption
	}
	updateContainerResourcesReturns struct {
		result1 *v1.UpdateContainerResourcesResponse
		result2 error
	}
	updateContainerResourcesReturnsOnCall map[int]struct {
		result1 *v1.UpdateContainerResourcesResponse
		result2 error
	}
	UpdateRuntimeConfigStub        func(context.Context, *v1.UpdateRuntimeConfigRequest, ...grpc.CallOption) (*v1.UpdateRuntimeConfigResponse, error)
	updateRuntimeConfigMutex       sync.RWMutex
	updateRuntimeConfigArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.UpdateRuntimeConfigRequest
		arg3 []grpc.CallOption
	}
	updateRuntimeConfigReturns struct {
		result1 *v1.UpdateRuntimeConfigResponse
		result2 error
	}
	updateRuntimeConfigReturnsOnCall map[int]struct {
		result1 *v1.UpdateRuntimeConfigResponse
		result2 error
	}
	VersionStub        func(context.Context, *v1.VersionRequest, ...grpc.CallOption) (*v1.VersionResponse, error)
	versionMutex       sync.RWMutex
	versionArgsForCall []struct {
		arg1 context.Context
		arg2 *v1.VersionRequest
		arg3 []grpc.CallOption
	}
	versionReturns struct {
		result1 *v1.VersionResponse
		result2 error
	}
	versionReturnsOnCall map[int]struct {
		result1 *v1.VersionResponse
		result2 error
	}
	invocations      map[string][][]interface{}
	invocationsMutex sync.RWMutex
}

func (fake *FakeRuntimeServiceClient) Attach(arg1 context.Context, arg2 *v1.AttachRequest, arg3 ...grpc.CallOption) (*v1.AttachResponse, error) {
	fake.attachMutex.Lock()
	ret, specificReturn := fake.attachReturnsOnCall[len(fake.attachArgsForCall)]
	fake.attachArgsForCall = append(fake.attachArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.AttachRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.AttachStub
	fakeReturns := fake.attachReturns
	fake.recordInvocation("Attach", []interface{}{arg1, arg2, arg3})
	fake.attachMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) AttachCallCount() int {
	fake.attachMutex.RLock()
	defer fake.attachMutex.RUnlock()
	return len(fake.attachArgsForCall)
}

func (fake *FakeRuntimeServiceClient) AttachCalls(stub func(context.Context, *v1.AttachRequest, ...grpc.CallOption) (*v1.AttachResponse, error)) {
	fake.attachMutex.Lock()
	defer fake.attachMutex.Unlock()
	fake.AttachStub = stub
}

func (fake *FakeRuntimeServiceClient) AttachArgsForCall(i int) (context.Context, *v1.AttachRequest, []grpc.CallOption) {
	fake.attachMutex.RLock()
	defer fake.attachMutex.RUnlock()
	argsForCall := fake.attachArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) AttachReturns(result1 *v1.AttachResponse, result2 error) {
	fake.attachMutex.Lock()
	defer fake.attachMutex.Unlock()
	fake.AttachStub = nil
	fake.attachReturns = struct {
		result1 *v1.AttachResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) AttachReturnsOnCall(i int, result1 *v1.AttachResponse, result2 error) {
	fake.attachMutex.Lock()
	defer fake.attachMutex.Unlock()
	fake.AttachStub = nil
	if fake.attachReturnsOnCall == nil {
		fake.attachReturnsOnCall = make(map[int]struct {
			result1 *v1.AttachResponse
			result2 error
		})
	}
	fake.attachReturnsOnCall[i] = struct {
		result1 *v1.AttachResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) CheckpointContainer(arg1 context.Context, arg2 *v1.CheckpointContainerRequest, arg3 ...grpc.CallOption) (*v1.CheckpointContainerResponse, error) {
	fake.checkpointContainerMutex.Lock()
	ret, specificReturn := fake.checkpointContainerReturnsOnCall[len(fake.checkpointContainerArgsForCall)]
	fake.checkpointContainerArgsForCall = append(fake.checkpointContainerArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.CheckpointContainerRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.CheckpointContainerStub
	fakeReturns := fake.checkpointContainerReturns
	fake.recordInvocation("CheckpointContainer", []interface{}{arg1, arg2, arg3})
	fake.checkpointContainerMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) CheckpointContainerCallCount() int {
	fake.checkpointContainerMutex.RLock()
	defer fake.checkpointContainerMutex.RUnlock()
	return len(fake.checkpointContainerArgsForCall)
}

func (fake *FakeRuntimeServiceClient) CheckpointContainerCalls(stub func(context.Context, *v1.CheckpointContainerRequest, ...grpc.CallOption) (*v1.CheckpointContainerResponse, error)) {
	fake.checkpointContainerMutex.Lock()
	defer fake.checkpointContainerMutex.Unlock()
	fake.CheckpointContainerStub = stub
}

func (fake *FakeRuntimeServiceClient) CheckpointContainerArgsForCall(i int) (context.Context, *v1.CheckpointContainerRequest, []grpc.CallOption) {
	fake.checkpointContainerMutex.RLock()
	defer fake.checkpointContainerMutex.RUnlock()
	argsForCall := fake.checkpointContainerArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) CheckpointContainerReturns(result1 *v1.CheckpointContainerResponse, result2 error) {
	fake.checkpointContainerMutex.Lock()
	defer fake.checkpointContainerMutex.Unlock()
	fake.CheckpointContainerStub = nil
	fake.checkpointContainerReturns = struct {
		result1 *v1.CheckpointContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) CheckpointContainerReturnsOnCall(i int, result1 *v1.CheckpointContainerResponse, result2 error) {
	fake.checkpointContainerMutex.Lock()
	defer fake.checkpointContainerMutex.Unlock()
	fake.CheckpointContainerStub = nil
	if fake.checkpointContainerReturnsOnCall == nil {
		fake.checkpointContainerReturnsOnCall = make(map[int]struct {
			result1 *v1.CheckpointContainerResponse
			result2 error
		})
	}
	fake.checkpointContainerReturnsOnCall[i] = struct {
		result1 *v1.CheckpointContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ContainerStats(arg1 context.Context, arg2 *v1.ContainerStatsRequest, arg3 ...grpc.CallOption) (*v1.ContainerStatsResponse, error) {
	fake.containerStatsMutex.Lock()
	ret, specificReturn := fake.containerStatsReturnsOnCall[len(fake.containerStatsArgsForCall)]
	fake.containerStatsArgsForCall = append(fake.containerStatsArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ContainerStatsRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ContainerStatsStub
	fakeReturns := fake.containerStatsReturns
	fake.recordInvocation("ContainerStats", []interface{}{arg1, arg2, arg3})
	fake.containerStatsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ContainerStatsCallCount() int {
	fake.containerStatsMutex.RLock()
	defer fake.containerStatsMutex.RUnlock()
	return len(fake.containerStatsArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ContainerStatsCalls(stub func(context.Context, *v1.ContainerStatsRequest, ...grpc.CallOption) (*v1.ContainerStatsResponse, error)) {
	fake.containerStatsMutex.Lock()
	defer fake.containerStatsMutex.Unlock()
	fake.ContainerStatsStub = stub
}

func (fake *FakeRuntimeServiceClient) ContainerStatsArgsForCall(i int) (context.Context, *v1.ContainerStatsRequest, []grpc.CallOption) {
	fake.containerStatsMutex.RLock()
	defer fake.containerStatsMutex.RUnlock()
	argsForCall := fake.containerStatsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ContainerStatsReturns(result1 *v1.ContainerStatsResponse, result2 error) {
	fake.containerStatsMutex.Lock()
	defer fake.containerStatsMutex.Unlock()
	fake.ContainerStatsStub = nil
	fake.containerStatsReturns = struct {
		result1 *v1.ContainerStatsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ContainerStatsReturnsOnCall(i int, result1 *v1.ContainerStatsResponse, result2 error) {
	fake.containerStatsMutex.Lock()
	defer fake.containerStatsMutex.Unlock()
	fake.ContainerStatsStub = nil
	if fake.containerStatsReturnsOnCall == nil {
		fake.containerStatsReturnsOnCall = make(map[int]struct {
			result1 *v1.ContainerStatsResponse
			result2 error
		})
	}
	fake.containerStatsReturnsOnCall[i] = struct {
		result1 *v1.ContainerStatsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ContainerStatus(arg1 context.Context, arg2 *v1.ContainerStatusRequest, arg3 ...grpc.CallOption) (*v1.ContainerStatusResponse, error) {
	fake.containerStatusMutex.Lock()
	ret, specificReturn := fake.containerStatusReturnsOnCall[len(fake.containerStatusArgsForCall)]
	fake.containerStatusArgsForCall = append(fake.containerStatusArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ContainerStatusRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ContainerStatusStub
	fakeReturns := fake.containerStatusReturns
	fake.recordInvocation("ContainerStatus", []interface{}{arg1, arg2, arg3})
	fake.containerStatusMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ContainerStatusCallCount() int {
	fake.containerStatusMutex.RLock()
	defer fake.containerStatusMutex.RUnlock()
	return len(fake.containerStatusArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ContainerStatusCalls(stub func(context.Context, *v1.ContainerStatusRequest, ...grpc.CallOption) (*v1.ContainerStatusResponse, error)) {
	fake.containerStatusMutex.Lock()
	defer fake.containerStatusMutex.Unlock()
	fake.ContainerStatusStub = stub
}

func (fake *FakeRuntimeServiceClient) ContainerStatusArgsForCall(i int) (context.Context, *v1.ContainerStatusRequest, []grpc.CallOption) {
	fake.containerStatusMutex.RLock()
	defer fake.containerStatusMutex.RUnlock()
	argsForCall := fake.containerStatusArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ContainerStatusReturns(result1 *v1.ContainerStatusResponse, result2 error) {
	fake.containerStatusMutex.Lock()
	defer fake.containerStatusMutex.Unlock()
	fake.ContainerStatusStub = nil
	fake.containerStatusReturns = struct {
		result1 *v1.ContainerStatusResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ContainerStatusReturnsOnCall(i int, result1 *v1.ContainerStatusResponse, result2 error) {
	fake.containerStatusMutex.Lock()
	defer fake.containerStatusMutex.Unlock()
	fake.ContainerStatusStub = nil
	if fake.containerStatusReturnsOnCall == nil {
		fake.containerStatusReturnsOnCall = make(map[int]struct {
			result1 *v1.ContainerStatusResponse
			result2 error
		})
	}
	fake.containerStatusReturnsOnCall[i] = struct {
		result1 *v1.ContainerStatusResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) CreateContainer(arg1 context.Context, arg2 *v1.CreateContainerRequest, arg3 ...grpc.CallOption) (*v1.CreateContainerResponse, error) {
	fake.createContainerMutex.Lock()
	ret, specificReturn := fake.createContainerReturnsOnCall[len(fake.createContainerArgsForCall)]
	fake.createContainerArgsForCall = append(fake.createContainerArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.CreateContainerRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.CreateContainerStub
	fakeReturns := fake.createContainerReturns
	fake.recordInvocation("CreateContainer", []interface{}{arg1, arg2, arg3})
	fake.createContainerMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) CreateContainerCallCount() int {
	fake.createContainerMutex.RLock()
	defer fake.createContainerMutex.RUnlock()
	return len(fake.createContainerArgsForCall)
}

func (fake *FakeRuntimeServiceClient) CreateContainerCalls(stub func(context.Context, *v1.CreateContainerRequest, ...grpc.CallOption) (*v1.CreateContainerResponse, error)) {
	fake.createContainerMutex.Lock()
	defer fake.createContainerMutex.Unlock()
	fake.CreateContainerStub = stub
}

func (fake *FakeRuntimeServiceClient) CreateContainerArgsForCall(i int) (context.Context, *v1.CreateContainerRequest, []grpc.CallOption) {
	fake.createContainerMutex.RLock()
	defer fake.createContainerMutex.RUnlock()
	argsForCall := fake.createContainerArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) CreateContainerReturns(result1 *v1.CreateContainerResponse, result2 error) {
	fake.createContainerMutex.Lock()
	defer fake.createContainerMutex.Unlock()
	fake.CreateContainerStub = nil
	fake.createContainerReturns = struct {
		result1 *v1.CreateContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) CreateContainerReturnsOnCall(i int, result1 *v1.CreateContainerResponse, result2 error) {
	fake.createContainerMutex.Lock()
	defer fake.createContainerMutex.Unlock()
	fake.CreateContainerStub = nil
	if fake.createContainerReturnsOnCall == nil {
		fake.createContainerReturnsOnCall = make(map[int]struct {
			result1 *v1.CreateContainerResponse
			result2 error
		})
	}
	fake.createContainerReturnsOnCall[i] = struct {
		result1 *v1.CreateContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) Exec(arg1 context.Context, arg2 *v1.ExecRequest, arg3 ...grpc.CallOption) (*v1.ExecResponse, error) {
	fake.execMutex.Lock()
	ret, specificReturn := fake.execReturnsOnCall[len(fake.execArgsForCall)]
	fake.execArgsForCall = append(fake.execArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ExecRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ExecStub
	fakeReturns := fake.execReturns
	fake.recordInvocation("Exec", []interface{}{arg1, arg2, arg3})
	fake.execMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ExecCallCount() int {
	fake.execMutex.RLock()
	defer fake.execMutex.RUnlock()
	return len(fake.execArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ExecCalls(stub func(context.Context, *v1.ExecRequest, ...grpc.CallOption) (*v1.ExecResponse, error)) {
	fake.execMutex.Lock()
	defer fake.execMutex.Unlock()
	fake.ExecStub = stub
}

func (fake *FakeRuntimeServiceClient) ExecArgsForCall(i int) (context.Context, *v1.ExecRequest, []grpc.CallOption) {
	fake.execMutex.RLock()
	defer fake.execMutex.RUnlock()
	argsForCall := fake.execArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ExecReturns(result1 *v1.ExecResponse, result2 error) {
	fake.execMutex.Lock()
	defer fake.execMutex.Unlock()
	fake.ExecStub = nil
	fake.execReturns = struct {
		result1 *v1.ExecResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ExecReturnsOnCall(i int, result1 *v1.ExecResponse, result2 error) {
	fake.execMutex.Lock()
	defer fake.execMutex.Unlock()
	fake.ExecStub = nil
	if fake.execReturnsOnCall == nil {
		fake.execReturnsOnCall = make(map[int]struct {
			result1 *v1.ExecResponse
			result2 error
		})
	}
	fake.execReturnsOnCall[i] = struct {
		result1 *v1.ExecResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ExecSync(arg1 context.Context, arg2 *v1.ExecSyncRequest, arg3 ...grpc.CallOption) (*v1.ExecSyncResponse, error) {
	fake.execSyncMutex.Lock()
	ret, specificReturn := fake.execSyncReturnsOnCall[len(fake.execSyncArgsForCall)]
	fake.execSyncArgsForCall = append(fake.execSyncArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ExecSyncRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ExecSyncStub
	fakeReturns := fake.execSyncReturns
	fake.recordInvocation("ExecSync", []interface{}{arg1, arg2, arg3})
	fake.execSyncMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ExecSyncCallCount() int {
	fake.execSyncMutex.RLock()
	defer fake.execSyncMutex.RUnlock()
	return len(fake.execSyncArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ExecSyncCalls(stub func(context.Context, *v1.ExecSyncRequest, ...grpc.CallOption) (*v1.ExecSyncResponse, error)) {
	fake.execSyncMutex.Lock()
	defer fake.execSyncMutex.Unlock()
	fake.ExecSyncStub = stub
}

func (fake *FakeRuntimeServiceClient) ExecSyncArgsForCall(i int) (context.Context, *v1.ExecSyncRequest, []grpc.CallOption) {
	fake.execSyncMutex.RLock()
	defer fake.execSyncMutex.RUnlock()
	argsForCall := fake.execSyncArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ExecSyncReturns(result1 *v1.ExecSyncResponse, result2 error) {
	fake.execSyncMutex.Lock()
	defer fake.execSyncMutex.Unlock()
	fake.ExecSyncStub = nil
	fake.execSyncReturns = struct {
		result1 *v1.ExecSyncResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ExecSyncReturnsOnCall(i int, result1 *v1.ExecSyncResponse, result2 error) {
	fake.execSyncMutex.Lock()
	defer fake.execSyncMutex.Unlock()
	fake.ExecSyncStub = nil
	if fake.execSyncReturnsOnCall == nil {
		fake.execSyncReturnsOnCall = make(map[int]struct {
			result1 *v1.ExecSyncResponse
			result2 error
		})
	}
	fake.execSyncReturnsOnCall[i] = struct {
		result1 *v1.ExecSyncResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) GetContainerEvents(arg1 context.Context, arg2 *v1.GetEventsRequest, arg3 ...grpc.CallOption) (v1.RuntimeService_GetContainerEventsClient, error) {
	fake.getContainerEventsMutex.Lock()
	ret, specificReturn := fake.getContainerEventsReturnsOnCall[len(fake.getContainerEventsArgsForCall)]
	fake.getContainerEventsArgsForCall = append(fake.getContainerEventsArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.GetEventsRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.GetContainerEventsStub
	fakeReturns := fake.getContainerEventsReturns
	fake.recordInvocation("GetContainerEvents", []interface{}{arg1, arg2, arg3})
	fake.getContainerEventsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) GetContainerEventsCallCount() int {
	fake.getContainerEventsMutex.RLock()
	defer fake.getContainerEventsMutex.RUnlock()
	return len(fake.getContainerEventsArgsForCall)
}

func (fake *FakeRuntimeServiceClient) GetContainerEventsCalls(stub func(context.Context, *v1.GetEventsRequest, ...grpc.CallOption) (v1.RuntimeService_GetContainerEventsClient, error)) {
	fake.getContainerEventsMutex.Lock()
	defer fake.getContainerEventsMutex.Unlock()
	fake.GetContainerEventsStub = stub
}

func (fake *FakeRuntimeServiceClient) GetContainerEventsArgsForCall(i int) (context.Context, *v1.GetEventsRequest, []grpc.CallOption) {
	fake.getContainerEventsMutex.RLock()
	defer fake.getContainerEventsMutex.RUnlock()
	argsForCall := fake.getContainerEventsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) GetContainerEventsReturns(result1 v1.RuntimeService_GetContainerEventsClient, result2 error) {
	fake.getContainerEventsMutex.Lock()
	defer fake.getContainerEventsMutex.Unlock()
	fake.GetContainerEventsStub = nil
	fake.getContainerEventsReturns = struct {
		result1 v1.RuntimeService_GetContainerEventsClient
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) GetContainerEventsReturnsOnCall(i int, result1 v1.RuntimeService_GetContainerEventsClient, result2 error) {
	fake.getContainerEventsMutex.Lock()
	defer fake.getContainerEventsMutex.Unlock()
	fake.GetContainerEventsStub = nil
	if fake.getContainerEventsReturnsOnCall == nil {
		fake.getContainerEventsReturnsOnCall = make(map[int]struct {
			result1 v1.RuntimeService_GetContainerEventsClient
			result2 error
		})
	}
	fake.getContainerEventsReturnsOnCall[i] = struct {
		result1 v1.RuntimeService_GetContainerEventsClient
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListContainerStats(arg1 context.Context, arg2 *v1.ListContainerStatsRequest, arg3 ...grpc.CallOption) (*v1.ListContainerStatsResponse, error) {
	fake.listContainerStatsMutex.Lock()
	ret, specificReturn := fake.listContainerStatsReturnsOnCall[len(fake.listContainerStatsArgsForCall)]
	fake.listContainerStatsArgsForCall = append(fake.listContainerStatsArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ListContainerStatsRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ListContainerStatsStub
	fakeReturns := fake.listContainerStatsReturns
	fake.recordInvocation("ListContainerStats", []interface{}{arg1, arg2, arg3})
	fake.listContainerStatsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ListContainerStatsCallCount() int {
	fake.listContainerStatsMutex.RLock()
	defer fake.listContainerStatsMutex.RUnlock()
	return len(fake.listContainerStatsArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ListContainerStatsCalls(stub func(context.Context, *v1.ListContainerStatsRequest, ...grpc.CallOption) (*v1.ListContainerStatsResponse, error)) {
	fake.listContainerStatsMutex.Lock()
	defer fake.listContainerStatsMutex.Unlock()
	fake.ListContainerStatsStub = stub
}

func (fake *FakeRuntimeServiceClient) ListContainerStatsArgsForCall(i int) (context.Context, *v1.ListContainerStatsRequest, []grpc.CallOption) {
	fake.listContainerStatsMutex.RLock()
	defer fake.listContainerStatsMutex.RUnlock()
	argsForCall := fake.listContainerStatsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ListContainerStatsReturns(result1 *v1.ListContainerStatsResponse, result2 error) {
	fake.listContainerStatsMutex.Lock()
	defer fake.listContainerStatsMutex.Unlock()
	fake.ListContainerStatsStub = nil
	fake.listContainerStatsReturns = struct {
		result1 *v1.ListContainerStatsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListContainerStatsReturnsOnCall(i int, result1 *v1.ListContainerStatsResponse, result2 error) {
	fake.listContainerStatsMutex.Lock()
	defer fake.listContainerStatsMutex.Unlock()
	fake.ListContainerStatsStub = nil
	if fake.listContainerStatsReturnsOnCall == nil {
		fake.listContainerStatsReturnsOnCall = make(map[int]struct {
			result1 *v1.ListContainerStatsResponse
			result2 error
		})
	}
	fake.listContainerStatsReturnsOnCall[i] = struct {
		result1 *v1.ListContainerStatsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListContainers(arg1 context.Context, arg2 *v1.ListContainersRequest, arg3 ...grpc.CallOption) (*v1.ListContainersResponse, error) {
	fake.listContainersMutex.Lock()
	ret, specificReturn := fake.listContainersReturnsOnCall[len(fake.listContainersArgsForCall)]
	fake.listContainersArgsForCall = append(fake.listContainersArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ListContainersRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ListContainersStub
	fakeReturns := fake.listContainersReturns
	fake.recordInvocation("ListContainers", []interface{}{arg1, arg2, arg3})
	fake.listContainersMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ListContainersCallCount() int {
	fake.listContainersMutex.RLock()
	defer fake.listContainersMutex.RUnlock()
	return len(fake.listContainersArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ListContainersCalls(stub func(context.Context, *v1.ListContainersRequest, ...grpc.CallOption) (*v1.ListContainersResponse, error)) {
	fake.listContainersMutex.Lock()
	defer fake.listContainersMutex.Unlock()
	fake.ListContainersStub = stub
}

func (fake *FakeRuntimeServiceClient) ListContainersArgsForCall(i int) (context.Context, *v1.ListContainersRequest, []grpc.CallOption) {
	fake.listContainersMutex.RLock()
	defer fake.listContainersMutex.RUnlock()
	argsForCall := fake.listContainersArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ListContainersReturns(result1 *v1.ListContainersResponse, result2 error) {
	fake.listContainersMutex.Lock()
	defer fake.listContainersMutex.Unlock()
	fake.ListContainersStub = nil
	fake.listContainersReturns = struct {
		result1 *v1.ListContainersResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListContainersReturnsOnCall(i int, result1 *v1.ListContainersResponse, result2 error) {
	fake.listContainersMutex.Lock()
	defer fake.listContainersMutex.Unlock()
	fake.ListContainersStub = nil
	if fake.listContainersReturnsOnCall == nil {
		fake.listContainersReturnsOnCall = make(map[int]struct {
			result1 *v1.ListContainersResponse
			result2 error
		})
	}
	fake.listContainersReturnsOnCall[i] = struct {
		result1 *v1.ListContainersResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListMetricDescriptors(arg1 context.Context, arg2 *v1.ListMetricDescriptorsRequest, arg3 ...grpc.CallOption) (*v1.ListMetricDescriptorsResponse, error) {
	fake.listMetricDescriptorsMutex.Lock()
	ret, specificReturn := fake.listMetricDescriptorsReturnsOnCall[len(fake.listMetricDescriptorsArgsForCall)]
	fake.listMetricDescriptorsArgsForCall = append(fake.listMetricDescriptorsArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ListMetricDescriptorsRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ListMetricDescriptorsStub
	fakeReturns := fake.listMetricDescriptorsReturns
	fake.recordInvocation("ListMetricDescriptors", []interface{}{arg1, arg2, arg3})
	fake.listMetricDescriptorsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ListMetricDescriptorsCallCount() int {
	fake.listMetricDescriptorsMutex.RLock()
	defer fake.listMetricDescriptorsMutex.RUnlock()
	return len(fake.listMetricDescriptorsArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ListMetricDescriptorsCalls(stub func(context.Context, *v1.ListMetricDescriptorsRequest, ...grpc.CallOption) (*v1.ListMetricDescriptorsResponse, error)) {
	fake.listMetricDescriptorsMutex.Lock()
	defer fake.listMetricDescriptorsMutex.Unlock()
	fake.ListMetricDescriptorsStub = stub
}

func (fake *FakeRuntimeServiceClient) ListMetricDescriptorsArgsForCall(i int) (context.Context, *v1.ListMetricDescriptorsRequest, []grpc.CallOption) {
	fake.listMetricDescriptorsMutex.RLock()
	defer fake.listMetricDescriptorsMutex.RUnlock()
	argsForCall := fake.listMetricDescriptorsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ListMetricDescriptorsReturns(result1 *v1.ListMetricDescriptorsResponse, result2 error) {
	fake.listMetricDescriptorsMutex.Lock()
	defer fake.listMetricDescriptorsMutex.Unlock()
	fake.ListMetricDescriptorsStub = nil
	fake.listMetricDescriptorsReturns = struct {
		result1 *v1.ListMetricDescriptorsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListMetricDescriptorsReturnsOnCall(i int, result1 *v1.ListMetricDescriptorsResponse, result2 error) {
	fake.listMetricDescriptorsMutex.Lock()
	defer fake.listMetricDescriptorsMutex.Unlock()
	fake.ListMetricDescriptorsStub = nil
	if fake.listMetricDescriptorsReturnsOnCall == nil {
		fake.listMetricDescriptorsReturnsOnCall = make(map[int]struct {
			result1 *v1.ListMetricDescriptorsResponse
			result2 error
		})
	}
	fake.listMetricDescriptorsReturnsOnCall[i] = struct {
		result1 *v1.ListMetricDescriptorsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListPodSandbox(arg1 context.Context, arg2 *v1.ListPodSandboxRequest, arg3 ...grpc.CallOption) (*v1.ListPodSandboxResponse, error) {
	fake.listPodSandboxMutex.Lock()
	ret, specificReturn := fake.listPodSandboxReturnsOnCall[len(fake.listPodSandboxArgsForCall)]
	fake.listPodSandboxArgsForCall = append(fake.listPodSandboxArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ListPodSandboxRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ListPodSandboxStub
	fakeReturns := fake.listPodSandboxReturns
	fake.recordInvocation("ListPodSandbox", []interface{}{arg1, arg2, arg3})
	fake.listPodSandboxMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxCallCount() int {
	fake.listPodSandboxMutex.RLock()
	defer fake.listPodSandboxMutex.RUnlock()
	return len(fake.listPodSandboxArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxCalls(stub func(context.Context, *v1.ListPodSandboxRequest, ...grpc.CallOption) (*v1.ListPodSandboxResponse, error)) {
	fake.listPodSandboxMutex.Lock()
	defer fake.listPodSandboxMutex.Unlock()
	fake.ListPodSandboxStub = stub
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxArgsForCall(i int) (context.Context, *v1.ListPodSandboxRequest, []grpc.CallOption) {
	fake.listPodSandboxMutex.RLock()
	defer fake.listPodSandboxMutex.RUnlock()
	argsForCall := fake.listPodSandboxArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxReturns(result1 *v1.ListPodSandboxResponse, result2 error) {
	fake.listPodSandboxMutex.Lock()
	defer fake.listPodSandboxMutex.Unlock()
	fake.ListPodSandboxStub = nil
	fake.listPodSandboxReturns = struct {
		result1 *v1.ListPodSandboxResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxReturnsOnCall(i int, result1 *v1.ListPodSandboxResponse, result2 error) {
	fake.listPodSandboxMutex.Lock()
	defer fake.listPodSandboxMutex.Unlock()
	fake.ListPodSandboxStub = nil
	if fake.listPodSandboxReturnsOnCall == nil {
		fake.listPodSandboxReturnsOnCall = make(map[int]struct {
			result1 *v1.ListPodSandboxResponse
			result2 error
		})
	}
	fake.listPodSandboxReturnsOnCall[i] = struct {
		result1 *v1.ListPodSandboxResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxMetrics(arg1 context.Context, arg2 *v1.ListPodSandboxMetricsRequest, arg3 ...grpc.CallOption) (*v1.ListPodSandboxMetricsResponse, error) {
	fake.listPodSandboxMetricsMutex.Lock()
	ret, specificReturn := fake.listPodSandboxMetricsReturnsOnCall[len(fake.listPodSandboxMetricsArgsForCall)]
	fake.listPodSandboxMetricsArgsForCall = append(fake.listPodSandboxMetricsArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ListPodSandboxMetricsRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ListPodSandboxMetricsStub
	fakeReturns := fake.listPodSandboxMetricsReturns
	fake.recordInvocation("ListPodSandboxMetrics", []interface{}{arg1, arg2, arg3})
	fake.listPodSandboxMetricsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxMetricsCallCount() int {
	fake.listPodSandboxMetricsMutex.RLock()
	defer fake.listPodSandboxMetricsMutex.RUnlock()
	return len(fake.listPodSandboxMetricsArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxMetricsCalls(stub func(context.Context, *v1.ListPodSandboxMetricsRequest, ...grpc.CallOption) (*v1.ListPodSandboxMetricsResponse, error)) {
	fake.listPodSandboxMetricsMutex.Lock()
	defer fake.listPodSandboxMetricsMutex.Unlock()
	fake.ListPodSandboxMetricsStub = stub
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxMetricsArgsForCall(i int) (context.Context, *v1.ListPodSandboxMetricsRequest, []grpc.CallOption) {
	fake.listPodSandboxMetricsMutex.RLock()
	defer fake.listPodSandboxMetricsMutex.RUnlock()
	argsForCall := fake.listPodSandboxMetricsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxMetricsReturns(result1 *v1.ListPodSandboxMetricsResponse, result2 error) {
	fake.listPodSandboxMetricsMutex.Lock()
	defer fake.listPodSandboxMetricsMutex.Unlock()
	fake.ListPodSandboxMetricsStub = nil
	fake.listPodSandboxMetricsReturns = struct {
		result1 *v1.ListPodSandboxMetricsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxMetricsReturnsOnCall(i int, result1 *v1.ListPodSandboxMetricsResponse, result2 error) {
	fake.listPodSandboxMetricsMutex.Lock()
	defer fake.listPodSandboxMetricsMutex.Unlock()
	fake.ListPodSandboxMetricsStub = nil
	if fake.listPodSandboxMetricsReturnsOnCall == nil {
		fake.listPodSandboxMetricsReturnsOnCall = make(map[int]struct {
			result1 *v1.ListPodSandboxMetricsResponse
			result2 error
		})
	}
	fake.listPodSandboxMetricsReturnsOnCall[i] = struct {
		result1 *v1.ListPodSandboxMetricsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxStats(arg1 context.Context, arg2 *v1.ListPodSandboxStatsRequest, arg3 ...grpc.CallOption) (*v1.ListPodSandboxStatsResponse, error) {
	fake.listPodSandboxStatsMutex.Lock()
	ret, specificReturn := fake.listPodSandboxStatsReturnsOnCall[len(fake.listPodSandboxStatsArgsForCall)]
	fake.listPodSandboxStatsArgsForCall = append(fake.listPodSandboxStatsArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ListPodSandboxStatsRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ListPodSandboxStatsStub
	fakeReturns := fake.listPodSandboxStatsReturns
	fake.recordInvocation("ListPodSandboxStats", []interface{}{arg1, arg2, arg3})
	fake.listPodSandboxStatsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxStatsCallCount() int {
	fake.listPodSandboxStatsMutex.RLock()
	defer fake.listPodSandboxStatsMutex.RUnlock()
	return len(fake.listPodSandboxStatsArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxStatsCalls(stub func(context.Context, *v1.ListPodSandboxStatsRequest, ...grpc.CallOption) (*v1.ListPodSandboxStatsResponse, error)) {
	fake.listPodSandboxStatsMutex.Lock()
	defer fake.listPodSandboxStatsMutex.Unlock()
	fake.ListPodSandboxStatsStub = stub
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxStatsArgsForCall(i int) (context.Context, *v1.ListPodSandboxStatsRequest, []grpc.CallOption) {
	fake.listPodSandboxStatsMutex.RLock()
	defer fake.listPodSandboxStatsMutex.RUnlock()
	argsForCall := fake.listPodSandboxStatsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxStatsReturns(result1 *v1.ListPodSandboxStatsResponse, result2 error) {
	fake.listPodSandboxStatsMutex.Lock()
	defer fake.listPodSandboxStatsMutex.Unlock()
	fake.ListPodSandboxStatsStub = nil
	fake.listPodSandboxStatsReturns = struct {
		result1 *v1.ListPodSandboxStatsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ListPodSandboxStatsReturnsOnCall(i int, result1 *v1.ListPodSandboxStatsResponse, result2 error) {
	fake.listPodSandboxStatsMutex.Lock()
	defer fake.listPodSandboxStatsMutex.Unlock()
	fake.ListPodSandboxStatsStub = nil
	if fake.listPodSandboxStatsReturnsOnCall == nil {
		fake.listPodSandboxStatsReturnsOnCall = make(map[int]struct {
			result1 *v1.ListPodSandboxStatsResponse
			result2 error
		})
	}
	fake.listPodSandboxStatsReturnsOnCall[i] = struct {
		result1 *v1.ListPodSandboxStatsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) PodSandboxStats(arg1 context.Context, arg2 *v1.PodSandboxStatsRequest, arg3 ...grpc.CallOption) (*v1.PodSandboxStatsResponse, error) {
	fake.podSandboxStatsMutex.Lock()
	ret, specificReturn := fake.podSandboxStatsReturnsOnCall[len(fake.podSandboxStatsArgsForCall)]
	fake.podSandboxStatsArgsForCall = append(fake.podSandboxStatsArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.PodSandboxStatsRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PodSandboxStatsStub
	fakeReturns := fake.podSandboxStatsReturns
	fake.recordInvocation("PodSandboxStats", []interface{}{arg1, arg2, arg3})
	fake.podSandboxStatsMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatsCallCount() int {
	fake.podSandboxStatsMutex.RLock()
	defer fake.podSandboxStatsMutex.RUnlock()
	return len(fake.podSandboxStatsArgsForCall)
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatsCalls(stub func(context.Context, *v1.PodSandboxStatsRequest, ...grpc.CallOption) (*v1.PodSandboxStatsResponse, error)) {
	fake.podSandboxStatsMutex.Lock()
	defer fake.podSandboxStatsMutex.Unlock()
	fake.PodSandboxStatsStub = stub
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatsArgsForCall(i int) (context.Context, *v1.PodSandboxStatsRequest, []grpc.CallOption) {
	fake.podSandboxStatsMutex.RLock()
	defer fake.podSandboxStatsMutex.RUnlock()
	argsForCall := fake.podSandboxStatsArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatsReturns(result1 *v1.PodSandboxStatsResponse, result2 error) {
	fake.podSandboxStatsMutex.Lock()
	defer fake.podSandboxStatsMutex.Unlock()
	fake.PodSandboxStatsStub = nil
	fake.podSandboxStatsReturns = struct {
		result1 *v1.PodSandboxStatsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatsReturnsOnCall(i int, result1 *v1.PodSandboxStatsResponse, result2 error) {
	fake.podSandboxStatsMutex.Lock()
	defer fake.podSandboxStatsMutex.Unlock()
	fake.PodSandboxStatsStub = nil
	if fake.podSandboxStatsReturnsOnCall == nil {
		fake.podSandboxStatsReturnsOnCall = make(map[int]struct {
			result1 *v1.PodSandboxStatsResponse
			result2 error
		})
	}
	fake.podSandboxStatsReturnsOnCall[i] = struct {
		result1 *v1.PodSandboxStatsResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatus(arg1 context.Context, arg2 *v1.PodSandboxStatusRequest, arg3 ...grpc.CallOption) (*v1.PodSandboxStatusResponse, error) {
	fake.podSandboxStatusMutex.Lock()
	ret, specificReturn := fake.podSandboxStatusReturnsOnCall[len(fake.podSandboxStatusArgsForCall)]
	fake.podSandboxStatusArgsForCall = append(fake.podSandboxStatusArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.PodSandboxStatusRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PodSandboxStatusStub
	fakeReturns := fake.podSandboxStatusReturns
	fake.recordInvocation("PodSandboxStatus", []interface{}{arg1, arg2, arg3})
	fake.podSandboxStatusMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatusCallCount() int {
	fake.podSandboxStatusMutex.RLock()
	defer fake.podSandboxStatusMutex.RUnlock()
	return len(fake.podSandboxStatusArgsForCall)
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatusCalls(stub func(context.Context, *v1.PodSandboxStatusRequest, ...grpc.CallOption) (*v1.PodSandboxStatusResponse, error)) {
	fake.podSandboxStatusMutex.Lock()
	defer fake.podSandboxStatusMutex.Unlock()
	fake.PodSandboxStatusStub = stub
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatusArgsForCall(i int) (context.Context, *v1.PodSandboxStatusRequest, []grpc.CallOption) {
	fake.podSandboxStatusMutex.RLock()
	defer fake.podSandboxStatusMutex.RUnlock()
	argsForCall := fake.podSandboxStatusArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatusReturns(result1 *v1.PodSandboxStatusResponse, result2 error) {
	fake.podSandboxStatusMutex.Lock()
	defer fake.podSandboxStatusMutex.Unlock()
	fake.PodSandboxStatusStub = nil
	fake.podSandboxStatusReturns = struct {
		result1 *v1.PodSandboxStatusResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) PodSandboxStatusReturnsOnCall(i int, result1 *v1.PodSandboxStatusResponse, result2 error) {
	fake.podSandboxStatusMutex.Lock()
	defer fake.podSandboxStatusMutex.Unlock()
	fake.PodSandboxStatusStub = nil
	if fake.podSandboxStatusReturnsOnCall == nil {
		fake.podSandboxStatusReturnsOnCall = make(map[int]struct {
			result1 *v1.PodSandboxStatusResponse
			result2 error
		})
	}
	fake.podSandboxStatusReturnsOnCall[i] = struct {
		result1 *v1.PodSandboxStatusResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) PortForward(arg1 context.Context, arg2 *v1.PortForwardRequest, arg3 ...grpc.CallOption) (*v1.PortForwardResponse, error) {
	fake.portForwardMutex.Lock()
	ret, specificReturn := fake.portForwardReturnsOnCall[len(fake.portForwardArgsForCall)]
	fake.portForwardArgsForCall = append(fake.portForwardArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.PortForwardRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.PortForwardStub
	fakeReturns := fake.portForwardReturns
	fake.recordInvocation("PortForward", []interface{}{arg1, arg2, arg3})
	fake.portForwardMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) PortForwardCallCount() int {
	fake.portForwardMutex.RLock()
	defer fake.portForwardMutex.RUnlock()
	return len(fake.portForwardArgsForCall)
}

func (fake *FakeRuntimeServiceClient) PortForwardCalls(stub func(context.Context, *v1.PortForwardRequest, ...grpc.CallOption) (*v1.PortForwardResponse, error)) {
	fake.portForwardMutex.Lock()
	defer fake.portForwardMutex.Unlock()
	fake.PortForwardStub = stub
}

func (fake *FakeRuntimeServiceClient) PortForwardArgsForCall(i int) (context.Context, *v1.PortForwardRequest, []grpc.CallOption) {
	fake.portForwardMutex.RLock()
	defer fake.portForwardMutex.RUnlock()
	argsForCall := fake.portForwardArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) PortForwardReturns(result1 *v1.PortForwardResponse, result2 error) {
	fake.portForwardMutex.Lock()
	defer fake.portForwardMutex.Unlock()
	fake.PortForwardStub = nil
	fake.portForwardReturns = struct {
		result1 *v1.PortForwardResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) PortForwardReturnsOnCall(i int, result1 *v1.PortForwardResponse, result2 error) {
	fake.portForwardMutex.Lock()
	defer fake.portForwardMutex.Unlock()
	fake.PortForwardStub = nil
	if fake.portForwardReturnsOnCall == nil {
		fake.portForwardReturnsOnCall = make(map[int]struct {
			result1 *v1.PortForwardResponse
			result2 error
		})
	}
	fake.portForwardReturnsOnCall[i] = struct {
		result1 *v1.PortForwardResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) RemoveContainer(arg1 context.Context, arg2 *v1.RemoveContainerRequest, arg3 ...grpc.CallOption) (*v1.RemoveContainerResponse, error) {
	fake.removeContainerMutex.Lock()
	ret, specificReturn := fake.removeContainerReturnsOnCall[len(fake.removeContainerArgsForCall)]
	fake.removeContainerArgsForCall = append(fake.removeContainerArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.RemoveContainerRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.RemoveContainerStub
	fakeReturns := fake.removeContainerReturns
	fake.recordInvocation("RemoveContainer", []interface{}{arg1, arg2, arg3})
	fake.removeContainerMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) RemoveContainerCallCount() int {
	fake.removeContainerMutex.RLock()
	defer fake.removeContainerMutex.RUnlock()
	return len(fake.removeContainerArgsForCall)
}

func (fake *FakeRuntimeServiceClient) RemoveContainerCalls(stub func(context.Context, *v1.RemoveContainerRequest, ...grpc.CallOption) (*v1.RemoveContainerResponse, error)) {
	fake.removeContainerMutex.Lock()
	defer fake.removeContainerMutex.Unlock()
	fake.RemoveContainerStub = stub
}

func (fake *FakeRuntimeServiceClient) RemoveContainerArgsForCall(i int) (context.Context, *v1.RemoveContainerRequest, []grpc.CallOption) {
	fake.removeContainerMutex.RLock()
	defer fake.removeContainerMutex.RUnlock()
	argsForCall := fake.removeContainerArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) RemoveContainerReturns(result1 *v1.RemoveContainerResponse, result2 error) {
	fake.removeContainerMutex.Lock()
	defer fake.removeContainerMutex.Unlock()
	fake.RemoveContainerStub = nil
	fake.removeContainerReturns = struct {
		result1 *v1.RemoveContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) RemoveContainerReturnsOnCall(i int, result1 *v1.RemoveContainerResponse, result2 error) {
	fake.removeContainerMutex.Lock()
	defer fake.removeContainerMutex.Unlock()
	fake.RemoveContainerStub = nil
	if fake.removeContainerReturnsOnCall == nil {
		fake.removeContainerReturnsOnCall = make(map[int]struct {
			result1 *v1.RemoveContainerResponse
			result2 error
		})
	}
	fake.removeContainerReturnsOnCall[i] = struct {
		result1 *v1.RemoveContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) RemovePodSandbox(arg1 context.Context, arg2 *v1.RemovePodSandboxRequest, arg3 ...grpc.CallOption) (*v1.RemovePodSandboxResponse, error) {
	fake.removePodSandboxMutex.Lock()
	ret, specificReturn := fake.removePodSandboxReturnsOnCall[len(fake.removePodSandboxArgsForCall)]
	fake.removePodSandboxArgsForCall = append(fake.removePodSandboxArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.RemovePodSandboxRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.RemovePodSandboxStub
	fakeReturns := fake.removePodSandboxReturns
	fake.recordInvocation("RemovePodSandbox", []interface{}{arg1, arg2, arg3})
	fake.removePodSandboxMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) RemovePodSandboxCallCount() int {
	fake.removePodSandboxMutex.RLock()
	defer fake.removePodSandboxMutex.RUnlock()
	return len(fake.removePodSandboxArgsForCall)
}

func (fake *FakeRuntimeServiceClient) RemovePodSandboxCalls(stub func(context.Context, *v1.RemovePodSandboxRequest, ...grpc.CallOption) (*v1.RemovePodSandboxResponse, error)) {
	fake.removePodSandboxMutex.Lock()
	defer fake.removePodSandboxMutex.Unlock()
	fake.RemovePodSandboxStub = stub
}

func (fake *FakeRuntimeServiceClient) RemovePodSandboxArgsForCall(i int) (context.Context, *v1.RemovePodSandboxRequest, []grpc.CallOption) {
	fake.removePodSandboxMutex.RLock()
	defer fake.removePodSandboxMutex.RUnlock()
	argsForCall := fake.removePodSandboxArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) RemovePodSandboxReturns(result1 *v1.RemovePodSandboxResponse, result2 error) {
	fake.removePodSandboxMutex.Lock()
	defer fake.removePodSandboxMutex.Unlock()
	fake.RemovePodSandboxStub = nil
	fake.removePodSandboxReturns = struct {
		result1 *v1.RemovePodSandboxResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) RemovePodSandboxReturnsOnCall(i int, result1 *v1.RemovePodSandboxResponse, result2 error) {
	fake.removePodSandboxMutex.Lock()
	defer fake.removePodSandboxMutex.Unlock()
	fake.RemovePodSandboxStub = nil
	if fake.removePodSandboxReturnsOnCall == nil {
		fake.removePodSandboxReturnsOnCall = make(map[int]struct {
			result1 *v1.RemovePodSandboxResponse
			result2 error
		})
	}
	fake.removePodSandboxReturnsOnCall[i] = struct {
		result1 *v1.RemovePodSandboxResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ReopenContainerLog(arg1 context.Context, arg2 *v1.ReopenContainerLogRequest, arg3 ...grpc.CallOption) (*v1.ReopenContainerLogResponse, error) {
	fake.reopenContainerLogMutex.Lock()
	ret, specificReturn := fake.reopenContainerLogReturnsOnCall[len(fake.reopenContainerLogArgsForCall)]
	fake.reopenContainerLogArgsForCall = append(fake.reopenContainerLogArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.ReopenContainerLogRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.ReopenContainerLogStub
	fakeReturns := fake.reopenContainerLogReturns
	fake.recordInvocation("ReopenContainerLog", []interface{}{arg1, arg2, arg3})
	fake.reopenContainerLogMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) ReopenContainerLogCallCount() int {
	fake.reopenContainerLogMutex.RLock()
	defer fake.reopenContainerLogMutex.RUnlock()
	return len(fake.reopenContainerLogArgsForCall)
}

func (fake *FakeRuntimeServiceClient) ReopenContainerLogCalls(stub func(context.Context, *v1.ReopenContainerLogRequest, ...grpc.CallOption) (*v1.ReopenContainerLogResponse, error)) {
	fake.reopenContainerLogMutex.Lock()
	defer fake.reopenContainerLogMutex.Unlock()
	fake.ReopenContainerLogStub = stub
}

func (fake *FakeRuntimeServiceClient) ReopenContainerLogArgsForCall(i int) (context.Context, *v1.ReopenContainerLogRequest, []grpc.CallOption) {
	fake.reopenContainerLogMutex.RLock()
	defer fake.reopenContainerLogMutex.RUnlock()
	argsForCall := fake.reopenContainerLogArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) ReopenContainerLogReturns(result1 *v1.ReopenContainerLogResponse, result2 error) {
	fake.reopenContainerLogMutex.Lock()
	defer fake.reopenContainerLogMutex.Unlock()
	fake.ReopenContainerLogStub = nil
	fake.reopenContainerLogReturns = struct {
		result1 *v1.ReopenContainerLogResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) ReopenContainerLogReturnsOnCall(i int, result1 *v1.ReopenContainerLogResponse, result2 error) {
	fake.reopenContainerLogMutex.Lock()
	defer fake.reopenContainerLogMutex.Unlock()
	fake.ReopenContainerLogStub = nil
	if fake.reopenContainerLogReturnsOnCall == nil {
		fake.reopenContainerLogReturnsOnCall = make(map[int]struct {
			result1 *v1.ReopenContainerLogResponse
			result2 error
		})
	}
	fake.reopenContainerLogReturnsOnCall[i] = struct {
		result1 *v1.ReopenContainerLogResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) RunPodSandbox(arg1 context.Context, arg2 *v1.RunPodSandboxRequest, arg3 ...grpc.CallOption) (*v1.RunPodSandboxResponse, error) {
	fake.runPodSandboxMutex.Lock()
	ret, specificReturn := fake.runPodSandboxReturnsOnCall[len(fake.runPodSandboxArgsForCall)]
	fake.runPodSandboxArgsForCall = append(fake.runPodSandboxArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.RunPodSandboxRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.RunPodSandboxStub
	fakeReturns := fake.runPodSandboxReturns
	fake.recordInvocation("RunPodSandbox", []interface{}{arg1, arg2, arg3})
	fake.runPodSandboxMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) RunPodSandboxCallCount() int {
	fake.runPodSandboxMutex.RLock()
	defer fake.runPodSandboxMutex.RUnlock()
	return len(fake.runPodSandboxArgsForCall)
}

func (fake *FakeRuntimeServiceClient) RunPodSandboxCalls(stub func(context.Context, *v1.RunPodSandboxRequest, ...grpc.CallOption) (*v1.RunPodSandboxResponse, error)) {
	fake.runPodSandboxMutex.Lock()
	defer fake.runPodSandboxMutex.Unlock()
	fake.RunPodSandboxStub = stub
}

func (fake *FakeRuntimeServiceClient) RunPodSandboxArgsForCall(i int) (context.Context, *v1.RunPodSandboxRequest, []grpc.CallOption) {
	fake.runPodSandboxMutex.RLock()
	defer fake.runPodSandboxMutex.RUnlock()
	argsForCall := fake.runPodSandboxArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) RunPodSandboxReturns(result1 *v1.RunPodSandboxResponse, result2 error) {
	fake.runPodSandboxMutex.Lock()
	defer fake.runPodSandboxMutex.Unlock()
	fake.RunPodSandboxStub = nil
	fake.runPodSandboxReturns = struct {
		result1 *v1.RunPodSandboxResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) RunPodSandboxReturnsOnCall(i int, result1 *v1.RunPodSandboxResponse, result2 error) {
	fake.runPodSandboxMutex.Lock()
	defer fake.runPodSandboxMutex.Unlock()
	fake.RunPodSandboxStub = nil
	if fake.runPodSandboxReturnsOnCall == nil {
		fake.runPodSandboxReturnsOnCall = make(map[int]struct {
			result1 *v1.RunPodSandboxResponse
			result2 error
		})
	}
	fake.runPodSandboxReturnsOnCall[i] = struct {
		result1 *v1.RunPodSandboxResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) RuntimeConfig(arg1 context.Context, arg2 *v1.RuntimeConfigRequest, arg3 ...grpc.CallOption) (*v1.RuntimeConfigResponse, error) {
	fake.runtimeConfigMutex.Lock()
	ret, specificReturn := fake.runtimeConfigReturnsOnCall[len(fake.runtimeConfigArgsForCall)]
	fake.runtimeConfigArgsForCall = append(fake.runtimeConfigArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.RuntimeConfigRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.RuntimeConfigStub
	fakeReturns := fake.runtimeConfigReturns
	fake.recordInvocation("RuntimeConfig", []interface{}{arg1, arg2, arg3})
	fake.runtimeConfigMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) RuntimeConfigCallCount() int {
	fake.runtimeConfigMutex.RLock()
	defer fake.runtimeConfigMutex.RUnlock()
	return len(fake.runtimeConfigArgsForCall)
}

func (fake *FakeRuntimeServiceClient) RuntimeConfigCalls(stub func(context.Context, *v1.RuntimeConfigRequest, ...grpc.CallOption) (*v1.RuntimeConfigResponse, error)) {
	fake.runtimeConfigMutex.Lock()
	defer fake.runtimeConfigMutex.Unlock()
	fake.RuntimeConfigStub = stub
}

func (fake *FakeRuntimeServiceClient) RuntimeConfigArgsForCall(i int) (context.Context, *v1.RuntimeConfigRequest, []grpc.CallOption) {
	fake.runtimeConfigMutex.RLock()
	defer fake.runtimeConfigMutex.RUnlock()
	argsForCall := fake.runtimeConfigArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) RuntimeConfigReturns(result1 *v1.RuntimeConfigResponse, result2 error) {
	fake.runtimeConfigMutex.Lock()
	defer fake.runtimeConfigMutex.Unlock()
	fake.RuntimeConfigStub = nil
	fake.runtimeConfigReturns = struct {
		result1 *v1.RuntimeConfigResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) RuntimeConfigReturnsOnCall(i int, result1 *v1.RuntimeConfigResponse, result2 error) {
	fake.runtimeConfigMutex.Lock()
	defer fake.runtimeConfigMutex.Unlock()
	fake.RuntimeConfigStub = nil
	if fake.runtimeConfigReturnsOnCall == nil {
		fake.runtimeConfigReturnsOnCall = make(map[int]struct {
			result1 *v1.RuntimeConfigResponse
			result2 error
		})
	}
	fake.runtimeConfigReturnsOnCall[i] = struct {
		result1 *v1.RuntimeConfigResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) StartContainer(arg1 context.Context, arg2 *v1.StartContainerRequest, arg3 ...grpc.CallOption) (*v1.StartContainerResponse, error) {
	fake.startContainerMutex.Lock()
	ret, specificReturn := fake.startContainerReturnsOnCall[len(fake.startContainerArgsForCall)]
	fake.startContainerArgsForCall = append(fake.startContainerArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.StartContainerRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.StartContainerStub
	fakeReturns := fake.startContainerReturns
	fake.recordInvocation("StartContainer", []interface{}{arg1, arg2, arg3})
	fake.startContainerMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) StartContainerCallCount() int {
	fake.startContainerMutex.RLock()
	defer fake.startContainerMutex.RUnlock()
	return len(fake.startContainerArgsForCall)
}

func (fake *FakeRuntimeServiceClient) StartContainerCalls(stub func(context.Context, *v1.StartContainerRequest, ...grpc.CallOption) (*v1.StartContainerResponse, error)) {
	fake.startContainerMutex.Lock()
	defer fake.startContainerMutex.Unlock()
	fake.StartContainerStub = stub
}

func (fake *FakeRuntimeServiceClient) StartContainerArgsForCall(i int) (context.Context, *v1.StartContainerRequest, []grpc.CallOption) {
	fake.startContainerMutex.RLock()
	defer fake.startContainerMutex.RUnlock()
	argsForCall := fake.startContainerArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) StartContainerReturns(result1 *v1.StartContainerResponse, result2 error) {
	fake.startContainerMutex.Lock()
	defer fake.startContainerMutex.Unlock()
	fake.StartContainerStub = nil
	fake.startContainerReturns = struct {
		result1 *v1.StartContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) StartContainerReturnsOnCall(i int, result1 *v1.StartContainerResponse, result2 error) {
	fake.startContainerMutex.Lock()
	defer fake.startContainerMutex.Unlock()
	fake.StartContainerStub = nil
	if fake.startContainerReturnsOnCall == nil {
		fake.startContainerReturnsOnCall = make(map[int]struct {
			result1 *v1.StartContainerResponse
			result2 error
		})
	}
	fake.startContainerReturnsOnCall[i] = struct {
		result1 *v1.StartContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) Status(arg1 context.Context, arg2 *v1.StatusRequest, arg3 ...grpc.CallOption) (*v1.StatusResponse, error) {
	fake.statusMutex.Lock()
	ret, specificReturn := fake.statusReturnsOnCall[len(fake.statusArgsForCall)]
	fake.statusArgsForCall = append(fake.statusArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.StatusRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.StatusStub
	fakeReturns := fake.statusReturns
	fake.recordInvocation("Status", []interface{}{arg1, arg2, arg3})
	fake.statusMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) StatusCallCount() int {
	fake.statusMutex.RLock()
	defer fake.statusMutex.RUnlock()
	return len(fake.statusArgsForCall)
}

func (fake *FakeRuntimeServiceClient) StatusCalls(stub func(context.Context, *v1.StatusRequest, ...grpc.CallOption) (*v1.StatusResponse, error)) {
	fake.statusMutex.Lock()
	defer fake.statusMutex.Unlock()
	fake.StatusStub = stub
}

func (fake *FakeRuntimeServiceClient) StatusArgsForCall(i int) (context.Context, *v1.StatusRequest, []grpc.CallOption) {
	fake.statusMutex.RLock()
	defer fake.statusMutex.RUnlock()
	argsForCall := fake.statusArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) StatusReturns(result1 *v1.StatusResponse, result2 error) {
	fake.statusMutex.Lock()
	defer fake.statusMutex.Unlock()
	fake.StatusStub = nil
	fake.statusReturns = struct {
		result1 *v1.StatusResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) StatusReturnsOnCall(i int, result1 *v1.StatusResponse, result2 error) {
	fake.statusMutex.Lock()
	defer fake.statusMutex.Unlock()
	fake.StatusStub = nil
	if fake.statusReturnsOnCall == nil {
		fake.statusReturnsOnCall = make(map[int]struct {
			result1 *v1.StatusResponse
			result2 error
		})
	}
	fake.statusReturnsOnCall[i] = struct {
		result1 *v1.StatusResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) StopContainer(arg1 context.Context, arg2 *v1.StopContainerRequest, arg3 ...grpc.CallOption) (*v1.StopContainerResponse, error) {
	fake.stopContainerMutex.Lock()
	ret, specificReturn := fake.stopContainerReturnsOnCall[len(fake.stopContainerArgsForCall)]
	fake.stopContainerArgsForCall = append(fake.stopContainerArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.StopContainerRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.StopContainerStub
	fakeReturns := fake.stopContainerReturns
	fake.recordInvocation("StopContainer", []interface{}{arg1, arg2, arg3})
	fake.stopContainerMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) StopContainerCallCount() int {
	fake.stopContainerMutex.RLock()
	defer fake.stopContainerMutex.RUnlock()
	return len(fake.stopContainerArgsForCall)
}

func (fake *FakeRuntimeServiceClient) StopContainerCalls(stub func(context.Context, *v1.StopContainerRequest, ...grpc.CallOption) (*v1.StopContainerResponse, error)) {
	fake.stopContainerMutex.Lock()
	defer fake.stopContainerMutex.Unlock()
	fake.StopContainerStub = stub
}

func (fake *FakeRuntimeServiceClient) StopContainerArgsForCall(i int) (context.Context, *v1.StopContainerRequest, []grpc.CallOption) {
	fake.stopContainerMutex.RLock()
	defer fake.stopContainerMutex.RUnlock()
	argsForCall := fake.stopContainerArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) StopContainerReturns(result1 *v1.StopContainerResponse, result2 error) {
	fake.stopContainerMutex.Lock()
	defer fake.stopContainerMutex.Unlock()
	fake.StopContainerStub = nil
	fake.stopContainerReturns = struct {
		result1 *v1.StopContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) StopContainerReturnsOnCall(i int, result1 *v1.StopContainerResponse, result2 error) {
	fake.stopContainerMutex.Lock()
	defer fake.stopContainerMutex.Unlock()
	fake.StopContainerStub = nil
	if fake.stopContainerReturnsOnCall == nil {
		fake.stopContainerReturnsOnCall = make(map[int]struct {
			result1 *v1.StopContainerResponse
			result2 error
		})
	}
	fake.stopContainerReturnsOnCall[i] = struct {
		result1 *v1.StopContainerResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) StopPodSandbox(arg1 context.Context, arg2 *v1.StopPodSandboxRequest, arg3 ...grpc.CallOption) (*v1.StopPodSandboxResponse, error) {
	fake.stopPodSandboxMutex.Lock()
	ret, specificReturn := fake.stopPodSandboxReturnsOnCall[len(fake.stopPodSandboxArgsForCall)]
	fake.stopPodSandboxArgsForCall = append(fake.stopPodSandboxArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.StopPodSandboxRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.StopPodSandboxStub
	fakeReturns := fake.stopPodSandboxReturns
	fake.recordInvocation("StopPodSandbox", []interface{}{arg1, arg2, arg3})
	fake.stopPodSandboxMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) StopPodSandboxCallCount() int {
	fake.stopPodSandboxMutex.RLock()
	defer fake.stopPodSandboxMutex.RUnlock()
	return len(fake.stopPodSandboxArgsForCall)
}

func (fake *FakeRuntimeServiceClient) StopPodSandboxCalls(stub func(context.Context, *v1.StopPodSandboxRequest, ...grpc.CallOption) (*v1.StopPodSandboxResponse, error)) {
	fake.stopPodSandboxMutex.Lock()
	defer fake.stopPodSandboxMutex.Unlock()
	fake.StopPodSandboxStub = stub
}

func (fake *FakeRuntimeServiceClient) StopPodSandboxArgsForCall(i int) (context.Context, *v1.StopPodSandboxRequest, []grpc.CallOption) {
	fake.stopPodSandboxMutex.RLock()
	defer fake.stopPodSandboxMutex.RUnlock()
	argsForCall := fake.stopPodSandboxArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) StopPodSandboxReturns(result1 *v1.StopPodSandboxResponse, result2 error) {
	fake.stopPodSandboxMutex.Lock()
	defer fake.stopPodSandboxMutex.Unlock()
	fake.StopPodSandboxStub = nil
	fake.stopPodSandboxReturns = struct {
		result1 *v1.StopPodSandboxResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) StopPodSandboxReturnsOnCall(i int, result1 *v1.StopPodSandboxResponse, result2 error) {
	fake.stopPodSandboxMutex.Lock()
	defer fake.stopPodSandboxMutex.Unlock()
	fake.StopPodSandboxStub = nil
	if fake.stopPodSandboxReturnsOnCall == nil {
		fake.stopPodSandboxReturnsOnCall = make(map[int]struct {
			result1 *v1.StopPodSandboxResponse
			result2 error
		})
	}
	fake.stopPodSandboxReturnsOnCall[i] = struct {
		result1 *v1.StopPodSandboxResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) UpdateContainerResources(arg1 context.Context, arg2 *v1.UpdateContainerResourcesRequest, arg3 ...grpc.CallOption) (*v1.UpdateContainerResourcesResponse, error) {
	fake.updateContainerResourcesMutex.Lock()
	ret, specificReturn := fake.updateContainerResourcesReturnsOnCall[len(fake.updateContainerResourcesArgsForCall)]
	fake.updateContainerResourcesArgsForCall = append(fake.updateContainerResourcesArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.UpdateContainerResourcesRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.UpdateContainerResourcesStub
	fakeReturns := fake.updateContainerResourcesReturns
	fake.recordInvocation("UpdateContainerResources", []interface{}{arg1, arg2, arg3})
	fake.updateContainerResourcesMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) UpdateContainerResourcesCallCount() int {
	fake.updateContainerResourcesMutex.RLock()
	defer fake.updateContainerResourcesMutex.RUnlock()
	return len(fake.updateContainerResourcesArgsForCall)
}

func (fake *FakeRuntimeServiceClient) UpdateContainerResourcesCalls(stub func(context.Context, *v1.UpdateContainerResourcesRequest, ...grpc.CallOption) (*v1.UpdateContainerResourcesResponse, error)) {
	fake.updateContainerResourcesMutex.Lock()
	defer fake.updateContainerResourcesMutex.Unlock()
	fake.UpdateContainerResourcesStub = stub
}

func (fake *FakeRuntimeServiceClient) UpdateContainerResourcesArgsForCall(i int) (context.Context, *v1.UpdateContainerResourcesRequest, []grpc.CallOption) {
	fake.updateContainerResourcesMutex.RLock()
	defer fake.updateContainerResourcesMutex.RUnlock()
	argsForCall := fake.updateContainerResourcesArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) UpdateContainerResourcesReturns(result1 *v1.UpdateContainerResourcesResponse, result2 error) {
	fake.updateContainerResourcesMutex.Lock()
	defer fake.updateContainerResourcesMutex.Unlock()
	fake.UpdateContainerResourcesStub = nil
	fake.updateContainerResourcesReturns = struct {
		result1 *v1.UpdateContainerResourcesResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) UpdateContainerResourcesReturnsOnCall(i int, result1 *v1.UpdateContainerResourcesResponse, result2 error) {
	fake.updateContainerResourcesMutex.Lock()
	defer fake.updateContainerResourcesMutex.Unlock()
	fake.UpdateContainerResourcesStub = nil
	if fake.updateContainerResourcesReturnsOnCall == nil {
		fake.updateContainerResourcesReturnsOnCall = make(map[int]struct {
			result1 *v1.UpdateContainerResourcesResponse
			result2 error
		})
	}
	fake.updateContainerResourcesReturnsOnCall[i] = struct {
		result1 *v1.UpdateContainerResourcesResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) UpdateRuntimeConfig(arg1 context.Context, arg2 *v1.UpdateRuntimeConfigRequest, arg3 ...grpc.CallOption) (*v1.UpdateRuntimeConfigResponse, error) {
	fake.updateRuntimeConfigMutex.Lock()
	ret, specificReturn := fake.updateRuntimeConfigReturnsOnCall[len(fake.updateRuntimeConfigArgsForCall)]
	fake.updateRuntimeConfigArgsForCall = append(fake.updateRuntimeConfigArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.UpdateRuntimeConfigRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.UpdateRuntimeConfigStub
	fakeReturns := fake.updateRuntimeConfigReturns
	fake.recordInvocation("UpdateRuntimeConfig", []interface{}{arg1, arg2, arg3})
	fake.updateRuntimeConfigMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) UpdateRuntimeConfigCallCount() int {
	fake.updateRuntimeConfigMutex.RLock()
	defer fake.updateRuntimeConfigMutex.RUnlock()
	return len(fake.updateRuntimeConfigArgsForCall)
}

func (fake *FakeRuntimeServiceClient) UpdateRuntimeConfigCalls(stub func(context.Context, *v1.UpdateRuntimeConfigRequest, ...grpc.CallOption) (*v1.UpdateRuntimeConfigResponse, error)) {
	fake.updateRuntimeConfigMutex.Lock()
	defer fake.updateRuntimeConfigMutex.Unlock()
	fake.UpdateRuntimeConfigStub = stub
}

func (fake *FakeRuntimeServiceClient) UpdateRuntimeConfigArgsForCall(i int) (context.Context, *v1.UpdateRuntimeConfigRequest, []grpc.CallOption) {
	fake.updateRuntimeConfigMutex.RLock()
	defer fake.updateRuntimeConfigMutex.RUnlock()
	argsForCall := fake.updateRuntimeConfigArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) UpdateRuntimeConfigReturns(result1 *v1.UpdateRuntimeConfigResponse, result2 error) {
	fake.updateRuntimeConfigMutex.Lock()
	defer fake.updateRuntimeConfigMutex.Unlock()
	fake.UpdateRuntimeConfigStub = nil
	fake.updateRuntimeConfigReturns = struct {
		result1 *v1.UpdateRuntimeConfigResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) UpdateRuntimeConfigReturnsOnCall(i int, result1 *v1.UpdateRuntimeConfigResponse, result2 error) {
	fake.updateRuntimeConfigMutex.Lock()
	defer fake.updateRuntimeConfigMutex.Unlock()
	fake.UpdateRuntimeConfigStub = nil
	if fake.updateRuntimeConfigReturnsOnCall == nil {
		fake.updateRuntimeConfigReturnsOnCall = make(map[int]struct {
			result1 *v1.UpdateRuntimeConfigResponse
			result2 error
		})
	}
	fake.updateRuntimeConfigReturnsOnCall[i] = struct {
		result1 *v1.UpdateRuntimeConfigResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) Version(arg1 context.Context, arg2 *v1.VersionRequest, arg3 ...grpc.CallOption) (*v1.VersionResponse, error) {
	fake.versionMutex.Lock()
	ret, specificReturn := fake.versionReturnsOnCall[len(fake.versionArgsForCall)]
	fake.versionArgsForCall = append(fake.versionArgsForCall, struct {
		arg1 context.Context
		arg2 *v1.VersionRequest
		arg3 []grpc.CallOption
	}{arg1, arg2, arg3})
	stub := fake.VersionStub
	fakeReturns := fake.versionReturns
	fake.recordInvocation("Version", []interface{}{arg1, arg2, arg3})
	fake.versionMutex.Unlock()
	if stub != nil {
		return stub(arg1, arg2, arg3...)
	}
	if specificReturn {
		return ret.result1, ret.result2
	}
	return fakeReturns.result1, fakeReturns.result2
}

func (fake *FakeRuntimeServiceClient) VersionCallCount() int {
	fake.versionMutex.RLock()
	defer fake.versionMutex.RUnlock()
	return len(fake.versionArgsForCall)
}

func (fake *FakeRuntimeServiceClient) VersionCalls(stub func(context.Context, *v1.VersionRequest, ...grpc.CallOption) (*v1.VersionResponse, error)) {
	fake.versionMutex.Lock()
	defer fake.versionMutex.Unlock()
	fake.VersionStub = stub
}

func (fake *FakeRuntimeServiceClient) VersionArgsForCall(i int) (context.Context, *v1.VersionRequest, []grpc.CallOption) {
	fake.versionMutex.RLock()
	defer fake.versionMutex.RUnlock()
	argsForCall := fake.versionArgsForCall[i]
	return argsForCall.arg1, argsForCall.arg2, argsForCall.arg3
}

func (fake *FakeRuntimeServiceClient) VersionReturns(result1 *v1.VersionResponse, result2 error) {
	fake.versionMutex.Lock()
	defer fake.versionMutex.Unlock()
	fake.VersionStub = nil
	fake.versionReturns = struct {
		result1 *v1.VersionResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) VersionReturnsOnCall(i int, result1 *v1.VersionResponse, result2 error) {
	fake.versionMutex.Lock()
	defer fake.versionMutex.Unlock()
	fake.VersionStub = nil
	if fake.versionReturnsOnCall == nil {
		fake.versionReturnsOnCall = make(map[int]struct {
			result1 *v1.VersionResponse
			result2 error
		})
	}
	fake.versionReturnsOnCall[i] = struct {
		result1 *v1.VersionResponse
		result2 error
	}{result1, result2}
}

func (fake *FakeRuntimeServiceClient) Invocations() map[string][][]interface{} {
	fake.invocationsMutex.RLock()
	defer fake.invocationsMutex.RUnlock()
	fake.attachMutex.RLock()
	defer fake.attachMutex.RUnlock()
	fake.checkpointContainerMutex.RLock()
	defer fake.checkpointContainerMutex.RUnlock()
	fake.containerStatsMutex.RLock()
	defer fake.containerStatsMutex.RUnlock()
	fake.containerStatusMutex.RLock()
	defer fake.containerStatusMutex.RUnlock()
	fake.createContainerMutex.RLock()
	defer fake.createContainerMutex.RUnlock()
	fake.execMutex.RLock()
	defer fake.execMutex.RUnlock()
	fake.execSyncMutex.RLock()
	defer fake.execSyncMutex.RUnlock()
	fake.getContainerEventsMutex.RLock()
	defer fake.getContainerEventsMutex.RUnlock()
	fake.listContainerStatsMutex.RLock()
	defer fake.listContainerStatsMutex.RUnlock()
	fake.listContainersMutex.RLock()
	defer fake.listContainersMutex.RUnlock()
	fake.listMetricDescriptorsMutex.RLock()
	defer fake.listMetricDescriptorsMutex.RUnlock()
	fake.listPodSandboxMutex.RLock()
	defer fake.listPodSandboxMutex.RUnlock()
	fake.listPodSandboxMetricsMutex.RLock()
	defer fake.listPodSandboxMetricsMutex.RUnlock()
	fake.listPodSandboxStatsMutex.RLock()
	defer fake.listPodSandboxStatsMutex.RUnlock()
	fake.podSandboxStatsMutex.RLock()
	defer fake.podSandboxStatsMutex.RUnlock()
	fake.podSandboxStatusMutex.RLock()
	defer fake.podSandboxStatusMutex.RUnlock()
	fake.portForwardMutex.RLock()
	defer fake.portForwardMutex.RUnlock()
	fake.removeContainerMutex.RLock()
	defer fake.removeContainerMutex.RUnlock()
	fake.removePodSandboxMutex.RLock()
	defer fake.removePodSandboxMutex.RUnlock()
	fake.reopenContainerLogMutex.RLock()
	defer fake.reopenContainerLogMutex.RUnlock()
	fake.runPodSandboxMutex.RLock()
	defer fake.runPodSandboxMutex.RUnlock()
	fake.runtimeConfigMutex.RLock()
	defer fake.runtimeConfigMutex.RUnlock()
	fake.startContainerMutex.RLock()
	defer fake.startContainerMutex.RUnlock()
	fake.statusMutex.RLock()
	defer fake.statusMutex.RUnlock()
	fake.stopContainerMutex.RLock()
	defer fake.stopContainerMutex.RUnlock()
	fake.stopPodSandboxMutex.RLock()
	defer fake.stopPodSandboxMutex.RUnlock()
	fake.updateContainerResourcesMutex.RLock()
	defer fake.updateContainerResourcesMutex.RUnlock()
	fake.updateRuntimeConfigMutex.RLock()
	defer fake.updateRuntimeConfigMutex.RUnlock()
	fake.versionMutex.RLock()
	defer fake.versionMutex.RUnlock()
	copiedInvocations := map[string][][]interface{}{}
	for key, value := range fake.invocations {
		copiedInvocations[key] = value
	}
	return copiedInvocations
}

func (fake *FakeRuntimeServiceClient) recordInvocation(key string, args []interface{}) {
	fake.invocationsMutex.Lock()
	defer fake.invocationsMutex.Unlock()
	if fake.invocations == nil {
		fake.invocations = map[string][][]interface{}{}
	}
	if fake.invocations[key] == nil {
		fake.invocations[key] = [][]interface{}{}
	}
	fake.invocations[key] = append(fake.invocations[key], args)
}

var _ v1.RuntimeServiceClient = new(FakeRuntimeServiceClient)
