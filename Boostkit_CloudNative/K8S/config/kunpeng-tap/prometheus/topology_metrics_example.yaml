# Topology-aware 策略监控指标查询示例
# 这些查询可以帮助监控容器在资源树中的分布情况和资源使用情况

# 1. 容器分布监控
# 查看每个 NUMA 节点上的容器数量
# tap_topology_container_count{numa_node="0", socket_id="0", die_id="0"}

# 查看所有 NUMA 节点的容器分布
# sum by (numa_node) (tap_topology_container_count)

# 查看每个 Socket 的容器分布
# sum by (socket_id) (tap_topology_container_count)

# 2. CPU 资源使用监控
# 查看每个 NUMA 节点的 CPU 使用情况（按类型）
# tap_topology_cpu_usage_millicores{numa_node="0", socket_id="0", die_id="0", type="allocated"}

# 查看所有 NUMA 节点的 CPU 使用情况
# sum by (numa_node, type) (tap_topology_cpu_usage_millicores)

# 查看 CPU 使用率（已分配 / 总容量）
# sum by (numa_node) (tap_topology_cpu_usage_millicores{type="allocated"}) / sum by (numa_node) (tap_topology_cpu_usage_millicores{type="limit"}) * 100

# 3. 内存资源使用监控
# 查看每个 NUMA 节点的内存使用情况
# tap_topology_memory_usage_kb{numa_node="0", socket_id="0", die_id="0"}

# 查看所有 NUMA 节点的内存使用情况
# sum by (numa_node) (tap_topology_memory_usage_kb)

# 4. 层次结构资源使用监控
# 查看不同层次的资源使用情况
# tap_topology_hierarchy_resource_usage{hierarchy_level="numa", resource_type="cpu", metric_type="allocated"}

# 查看 Socket 级别的资源使用
# tap_topology_hierarchy_resource_usage{hierarchy_level="socket", resource_type="cpu", metric_type="allocated"}

# 5. 资源分配成功率监控
# 查看资源分配成功次数
# tap_topology_allocation_total{status="success"}

# 查看资源分配失败次数
# tap_topology_allocation_total{status="failed"}

# 计算分配成功率
# sum(tap_topology_allocation_total{status="success"}) / sum(tap_topology_allocation_total) * 100

# 6. 资源传播延迟监控
# 查看资源传播操作的平均延迟
# histogram_quantile(0.95, sum by (operation, hierarchy_level) (rate(tap_topology_propagation_duration_seconds_bucket[5m])))

# 7. 告警规则示例
groups:
- name: topology_aware_alerts
  rules:
  - alert: HighCPUUsage
    expr: sum by (numa_node) (tap_topology_cpu_usage_millicores{type="allocated"}) / sum by (numa_node) (tap_topology_cpu_usage_millicores{type="limit"}) > 0.8
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High CPU usage on NUMA node {{ $labels.numa_node }}"
      description: "CPU usage is above 80% on NUMA node {{ $labels.numa_node }}"

  - alert: HighMemoryUsage
    expr: sum by (numa_node) (tap_topology_memory_usage_kb) > 1000000
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High memory usage on NUMA node {{ $labels.numa_node }}"
      description: "Memory usage is above 1GB on NUMA node {{ $labels.numa_node }}"

  - alert: ContainerDistributionImbalance
    expr: max(tap_topology_container_count) - min(tap_topology_container_count) > 5
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Container distribution imbalance detected"
      description: "Container distribution is imbalanced across NUMA nodes"

  - alert: AllocationFailure
    expr: rate(tap_topology_allocation_total{status="failed"}[5m]) > 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Resource allocation failures detected"
      description: "Resource allocation failures are occurring"

# 8. Grafana 仪表板查询示例
# 容器分布饼图
# sum by (numa_node) (tap_topology_container_count)

# CPU 使用率趋势图
# sum by (numa_node) (tap_topology_cpu_usage_millicores{type="allocated"})

# 内存使用率趋势图
# sum by (numa_node) (tap_topology_memory_usage_kb)

# 层次结构资源使用热力图
# tap_topology_hierarchy_resource_usage{resource_type="cpu", metric_type="allocated"} 